<template>
  <footer class="app-footer">
    <div class="footer-content">
      <div class="footer-left">
        <p class="copyright">
          © 2024 她的日记. All rights reserved.
        </p>
      </div>
      
      <div class="footer-center">
        <div class="footer-links">
          <a href="#" class="footer-link">关于我们</a>
          <a href="#" class="footer-link">隐私政策</a>
          <a href="#" class="footer-link">服务条款</a>
          <a href="#" class="footer-link">帮助中心</a>
        </div>
      </div>
      
      <div class="footer-right">
        <div class="social-links">
          <el-tooltip content="GitHub" placement="top">
            <a href="#" class="social-link">
              <el-icon><Platform /></el-icon>
            </a>
          </el-tooltip>
          <el-tooltip content="微博" placement="top">
            <a href="#" class="social-link">
              <el-icon><Share /></el-icon>
            </a>
          </el-tooltip>
          <el-tooltip content="邮箱" placement="top">
            <a href="mailto:<EMAIL>" class="social-link">
              <el-icon><Message /></el-icon>
            </a>
          </el-tooltip>
        </div>
      </div>
    </div>
    
    <div class="footer-bottom">
      <div class="tech-info">
        <span class="tech-item">
          <el-icon><Monitor /></el-icon>
          Vue 3 + TypeScript
        </span>
        <span class="tech-item">
          <el-icon><Connection /></el-icon>
          Element Plus
        </span>
        <span class="tech-item">
          <el-icon><DataBoard /></el-icon>
          Pinia
        </span>
      </div>
    </div>
  </footer>
</template>

<script setup lang="ts">
import {
  Platform,
  Share,
  Message,
  Monitor,
  Connection,
  DataBoard
} from '@element-plus/icons-vue'
</script>

<style lang="scss" scoped>
@import '@/assets/styles/variables.scss';

.app-footer {
  background: var(--surface-color);
  border-top: 1px solid var(--border-color);
  padding: var(--spacing-lg) 0;
  margin-top: auto;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--spacing-md);
}

.footer-left {
  .copyright {
    margin: 0;
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
  }
}

.footer-center {
  .footer-links {
    display: flex;
    gap: var(--spacing-lg);
  }

  .footer-link {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition-fast);

    &:hover {
      color: var(--primary-color);
    }
  }
}

.footer-right {
  .social-links {
    display: flex;
    gap: var(--spacing-sm);
  }

  .social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: var(--background-color);
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition-fast);

    &:hover {
      background: var(--primary-color);
      color: white;
      transform: translateY(-2px);
    }
  }
}

.footer-bottom {
  max-width: 1200px;
  margin: var(--spacing-md) auto 0;
  padding: var(--spacing-md) var(--spacing-lg) 0;
  border-top: 1px solid var(--border-color);

  .tech-info {
    display: flex;
    justify-content: center;
    gap: var(--spacing-lg);
    flex-wrap: wrap;
  }

  .tech-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-xs);
    color: var(--text-placeholder);

    .el-icon {
      font-size: 14px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .footer-content {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-sm);
  }

  .footer-center {
    .footer-links {
      flex-wrap: wrap;
      justify-content: center;
      gap: var(--spacing-md);
    }
  }

  .footer-bottom {
    .tech-info {
      gap: var(--spacing-md);
    }

    .tech-item {
      font-size: 10px;
    }
  }
}

@media (max-width: 480px) {
  .app-footer {
    padding: var(--spacing-md) 0;
  }

  .footer-content {
    padding: 0 var(--spacing-md);
  }

  .footer-center {
    .footer-links {
      gap: var(--spacing-sm);
    }

    .footer-link {
      font-size: 12px;
    }
  }

  .footer-bottom {
    padding: var(--spacing-sm) var(--spacing-md) 0;

    .tech-info {
      flex-direction: column;
      gap: var(--spacing-xs);
    }
  }
}
</style>
