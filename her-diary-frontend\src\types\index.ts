// 通用类型定义

// 基础响应类型
export interface BaseResponse<T = any> {
  code: number
  message: string
  data: T
}

// 分页响应类型
export interface PaginatedResponse<T = any> {
  data: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

// 分页请求参数
export interface PaginationParams {
  page?: number
  pageSize?: number
}

// 排序参数
export interface SortParams {
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

// 搜索参数
export interface SearchParams {
  keyword?: string
}

// 日期范围参数
export interface DateRangeParams {
  startDate?: string
  endDate?: string
}

// 文件上传响应
export interface UploadResponse {
  url: string
  filename: string
  size: number
  type: string
}

// 用户类型
export interface User {
  id: number
  username: string
  email: string
  nickname?: string
  avatar?: string
  created_at: string
  updated_at: string
}

// 日记类型
export interface Diary {
  id: number
  title: string
  content: string
  mood?: string
  weather?: string
  location?: string
  is_public: boolean
  created_at: string
  updated_at: string
  tags?: Tag[]
  images?: string[]
  user_id: number
}

// 标签类型
export interface Tag {
  id: number
  name: string
  color: string
  description?: string
  diary_count?: number
  created_at: string
  updated_at: string
}

// 心情类型
export interface Mood {
  value: string
  label: string
  color: string
  icon: string
}

// 天气类型
export interface Weather {
  value: string
  label: string
  icon: string
}

// 统计数据类型
export interface DashboardStats {
  totalDiaries: number
  totalWords: number
  totalTags: number
  writingDays: number
  averageWordsPerDiary: number
  longestStreak: number
  currentStreak: number
}

// 心情统计类型
export interface MoodStats {
  mood: string
  count: number
  percentage: number
  color: string
}

// 写作趋势类型
export interface WritingTrend {
  date: string
  count: number
  words: number
}

// 标签使用统计类型
export interface TagUsageStats {
  tag: Tag
  count: number
  percentage: number
}

// 表单验证规则类型
export interface ValidationRule {
  required?: boolean
  message?: string
  trigger?: string | string[]
  min?: number
  max?: number
  pattern?: RegExp
  validator?: (rule: any, value: any, callback: any) => void
}

// 菜单项类型
export interface MenuItem {
  id: string
  name: string
  path?: string
  icon?: string
  children?: MenuItem[]
  hidden?: boolean
  meta?: {
    title?: string
    requiresAuth?: boolean
    keepAlive?: boolean
  }
}

// 面包屑类型
export interface Breadcrumb {
  name: string
  path?: string
}

// 通知类型
export interface Notification {
  id: string
  title: string
  content: string
  type: 'info' | 'success' | 'warning' | 'error'
  read: boolean
  created_at: string
}

// 设置类型
export interface AppSettings {
  showBreadcrumb: boolean
  showTagsView: boolean
  showSidebarLogo: boolean
  fixedHeader: boolean
  sidebarTextTheme: boolean
  showThemeSwitch: boolean
  showLanguageSwitch: boolean
  showFullScreen: boolean
  showNotification: boolean
  showSearch: boolean
}

// 主题类型
export type Theme = 'light' | 'dark' | 'auto'

// 语言类型
export type Language = 'zh-CN' | 'en-US'

// 布局类型
export type Layout = 'default' | 'compact' | 'wide'

// 设备类型
export type Device = 'mobile' | 'tablet' | 'desktop'

// 侧边栏状态类型
export type SidebarStatus = 'opened' | 'closed'

// 加载状态类型
export interface LoadingState {
  loading: boolean
  error?: string | null
}

// API错误类型
export interface ApiError {
  code: number
  message: string
  details?: any
}

// 路由元信息类型
export interface RouteMeta {
  title?: string
  requiresAuth?: boolean
  requiresGuest?: boolean
  layout?: 'default' | 'auth' | 'blank'
  icon?: string
  hidden?: boolean
  keepAlive?: boolean
}

// 表单数据类型
export interface LoginForm {
  username: string
  password: string
}

export interface RegisterForm {
  username: string
  email: string
  password: string
  confirmPassword: string
  nickname?: string
}

export interface DiaryForm {
  title: string
  content: string
  mood?: string
  weather?: string
  location?: string
  is_public?: boolean
  tagIds?: number[]
  images?: string[]
}

export interface TagForm {
  name: string
  color: string
  description?: string
}

export interface ProfileForm {
  username: string
  email: string
  nickname?: string
}

export interface PasswordForm {
  oldPassword: string
  newPassword: string
  confirmPassword: string
}

// 查询参数类型
export interface DiaryListParams extends PaginationParams, SortParams, SearchParams, DateRangeParams {
  mood?: string
  weather?: string
  tagIds?: number[]
}

export interface TagListParams extends PaginationParams, SortParams, SearchParams {}

// 导出类型
export interface ExportParams {
  format: 'pdf' | 'docx' | 'txt'
  ids?: number[]
  startDate?: string
  endDate?: string
}

// 统计查询参数类型
export interface StatsParams extends DateRangeParams {
  period?: 'day' | 'week' | 'month' | 'year'
  type?: 'count' | 'words' | 'both'
}

// 组件属性类型
export interface LoadingSpinnerProps {
  size?: number
  text?: string
  fullscreen?: boolean
  overlay?: boolean
}

export interface DiaryCardProps {
  diary: Diary
  showActions?: boolean
  onClick?: (diary: Diary) => void
  onEdit?: (diary: Diary) => void
  onDelete?: (diary: Diary) => void
}

// 事件类型
export interface DiaryEvent {
  type: 'create' | 'update' | 'delete'
  diary: Diary
  timestamp: string
}

export interface UserEvent {
  type: 'login' | 'logout' | 'register' | 'profile_update'
  user?: User
  timestamp: string
}

// 缓存类型
export interface CacheItem<T = any> {
  data: T
  timestamp: number
  ttl: number
}

// 工具函数类型
export type Debounced<T extends (...args: any[]) => any> = (...args: Parameters<T>) => void

export type Throttled<T extends (...args: any[]) => any> = (...args: Parameters<T>) => void

// 状态管理类型
export interface AuthState {
  token: string
  user: User | null
  loading: boolean
}

export interface DiaryState {
  diaries: Diary[]
  currentDiary: Diary | null
  loading: boolean
  listLoading: boolean
  total: number
  currentPage: number
  pageSize: number
  searchParams: DiaryListParams
}

export interface AppState {
  theme: Theme
  language: Language
  layout: Layout
  sidebarStatus: SidebarStatus
  loading: boolean
  device: Device
  pageTitle: string
  breadcrumbs: Breadcrumb[]
  settings: AppSettings
}

// 默认导出所有类型
export default {
  // 基础类型
  BaseResponse,
  PaginatedResponse,
  PaginationParams,
  SortParams,
  SearchParams,
  DateRangeParams,
  UploadResponse,
  
  // 业务类型
  User,
  Diary,
  Tag,
  Mood,
  Weather,
  DashboardStats,
  MoodStats,
  WritingTrend,
  TagUsageStats,
  
  // 表单类型
  LoginForm,
  RegisterForm,
  DiaryForm,
  TagForm,
  ProfileForm,
  PasswordForm,
  
  // 查询参数类型
  DiaryListParams,
  TagListParams,
  ExportParams,
  StatsParams,
  
  // 应用类型
  Theme,
  Language,
  Layout,
  Device,
  SidebarStatus,
  AppSettings,
  RouteMeta,
  
  // 状态类型
  AuthState,
  DiaryState,
  AppState
}
