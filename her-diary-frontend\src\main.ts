// 导入全局样式
import './assets/styles/index.scss'

// Vue核心
import { createApp } from 'vue'
import App from './App.vue'

// Element Plus
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'

// 路由
import router from './router'

// 状态管理
import { createPinia } from 'pinia'

// 创建应用实例
const app = createApp(App)

// 使用插件
app.use(ElementPlus)
app.use(router)
app.use(createPinia())

// 挂载应用
app.mount('#app')
