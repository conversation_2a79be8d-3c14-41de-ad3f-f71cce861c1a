import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import utc from 'dayjs/plugin/utc'
import timezone from 'dayjs/plugin/timezone'
import 'dayjs/locale/zh-cn'

// 扩展dayjs插件
dayjs.extend(relativeTime)
dayjs.extend(utc)
dayjs.extend(timezone)
dayjs.locale('zh-cn')

// 日期格式常量
export const DATE_FORMATS = {
  DATE: 'YYYY-MM-DD',
  TIME: 'HH:mm:ss',
  DATETIME: 'YYYY-MM-DD HH:mm:ss',
  DATETIME_SHORT: 'MM-DD HH:mm',
  MONTH_DAY: 'MM月DD日',
  YEAR_MONTH: 'YYYY年MM月',
  FULL_DATE: 'YYYY年MM月DD日',
  FULL_DATETIME: 'YYYY年MM月DD日 HH:mm:ss',
  ISO: 'YYYY-MM-DDTHH:mm:ss.SSS[Z]'
}

/**
 * 格式化日期
 * @param date 日期
 * @param format 格式
 * @returns 格式化后的日期字符串
 */
export const formatDate = (date: string | Date | dayjs.Dayjs, format: string = DATE_FORMATS.DATETIME): string => {
  if (!date) return ''
  return dayjs(date).format(format)
}

/**
 * 格式化相对时间
 * @param date 日期
 * @returns 相对时间字符串
 */
export const formatRelativeTime = (date: string | Date | dayjs.Dayjs): string => {
  if (!date) return ''
  return dayjs(date).fromNow()
}

/**
 * 获取今天的日期
 * @param format 格式
 * @returns 今天的日期字符串
 */
export const getToday = (format: string = DATE_FORMATS.DATE): string => {
  return dayjs().format(format)
}

/**
 * 获取昨天的日期
 * @param format 格式
 * @returns 昨天的日期字符串
 */
export const getYesterday = (format: string = DATE_FORMATS.DATE): string => {
  return dayjs().subtract(1, 'day').format(format)
}

/**
 * 获取明天的日期
 * @param format 格式
 * @returns 明天的日期字符串
 */
export const getTomorrow = (format: string = DATE_FORMATS.DATE): string => {
  return dayjs().add(1, 'day').format(format)
}

/**
 * 获取本周的开始日期
 * @param format 格式
 * @returns 本周开始日期字符串
 */
export const getWeekStart = (format: string = DATE_FORMATS.DATE): string => {
  return dayjs().startOf('week').format(format)
}

/**
 * 获取本周的结束日期
 * @param format 格式
 * @returns 本周结束日期字符串
 */
export const getWeekEnd = (format: string = DATE_FORMATS.DATE): string => {
  return dayjs().endOf('week').format(format)
}

/**
 * 获取本月的开始日期
 * @param format 格式
 * @returns 本月开始日期字符串
 */
export const getMonthStart = (format: string = DATE_FORMATS.DATE): string => {
  return dayjs().startOf('month').format(format)
}

/**
 * 获取本月的结束日期
 * @param format 格式
 * @returns 本月结束日期字符串
 */
export const getMonthEnd = (format: string = DATE_FORMATS.DATE): string => {
  return dayjs().endOf('month').format(format)
}

/**
 * 获取本年的开始日期
 * @param format 格式
 * @returns 本年开始日期字符串
 */
export const getYearStart = (format: string = DATE_FORMATS.DATE): string => {
  return dayjs().startOf('year').format(format)
}

/**
 * 获取本年的结束日期
 * @param format 格式
 * @returns 本年结束日期字符串
 */
export const getYearEnd = (format: string = DATE_FORMATS.DATE): string => {
  return dayjs().endOf('year').format(format)
}

/**
 * 计算两个日期之间的天数差
 * @param startDate 开始日期
 * @param endDate 结束日期
 * @returns 天数差
 */
export const getDaysDiff = (startDate: string | Date | dayjs.Dayjs, endDate: string | Date | dayjs.Dayjs): number => {
  return dayjs(endDate).diff(dayjs(startDate), 'day')
}

/**
 * 判断是否为今天
 * @param date 日期
 * @returns 是否为今天
 */
export const isToday = (date: string | Date | dayjs.Dayjs): boolean => {
  return dayjs(date).isSame(dayjs(), 'day')
}

/**
 * 判断是否为昨天
 * @param date 日期
 * @returns 是否为昨天
 */
export const isYesterday = (date: string | Date | dayjs.Dayjs): boolean => {
  return dayjs(date).isSame(dayjs().subtract(1, 'day'), 'day')
}

/**
 * 判断是否为本周
 * @param date 日期
 * @returns 是否为本周
 */
export const isThisWeek = (date: string | Date | dayjs.Dayjs): boolean => {
  return dayjs(date).isSame(dayjs(), 'week')
}

/**
 * 判断是否为本月
 * @param date 日期
 * @returns 是否为本月
 */
export const isThisMonth = (date: string | Date | dayjs.Dayjs): boolean => {
  return dayjs(date).isSame(dayjs(), 'month')
}

/**
 * 判断是否为本年
 * @param date 日期
 * @returns 是否为本年
 */
export const isThisYear = (date: string | Date | dayjs.Dayjs): boolean => {
  return dayjs(date).isSame(dayjs(), 'year')
}

/**
 * 智能格式化日期（根据时间距离选择合适的格式）
 * @param date 日期
 * @returns 格式化后的日期字符串
 */
export const smartFormatDate = (date: string | Date | dayjs.Dayjs): string => {
  if (!date) return ''
  
  const target = dayjs(date)
  const now = dayjs()
  
  if (target.isSame(now, 'day')) {
    return target.format('HH:mm')
  } else if (target.isSame(now.subtract(1, 'day'), 'day')) {
    return `昨天 ${target.format('HH:mm')}`
  } else if (target.isSame(now, 'year')) {
    return target.format('MM-DD HH:mm')
  } else {
    return target.format('YYYY-MM-DD')
  }
}

/**
 * 获取日期范围
 * @param type 类型：week, month, year
 * @param offset 偏移量，0为当前，-1为上一个，1为下一个
 * @returns 日期范围 [开始日期, 结束日期]
 */
export const getDateRange = (
  type: 'week' | 'month' | 'year',
  offset: number = 0
): [string, string] => {
  const base = dayjs().add(offset, type)
  const start = base.startOf(type).format(DATE_FORMATS.DATE)
  const end = base.endOf(type).format(DATE_FORMATS.DATE)
  return [start, end]
}

/**
 * 解析日期字符串
 * @param dateString 日期字符串
 * @returns dayjs对象
 */
export const parseDate = (dateString: string): dayjs.Dayjs => {
  return dayjs(dateString)
}

/**
 * 验证日期格式
 * @param dateString 日期字符串
 * @param format 期望的格式
 * @returns 是否有效
 */
export const isValidDate = (dateString: string, format?: string): boolean => {
  if (format) {
    return dayjs(dateString, format, true).isValid()
  }
  return dayjs(dateString).isValid()
}

/**
 * 获取时区
 * @returns 当前时区
 */
export const getTimezone = (): string => {
  return dayjs.tz.guess()
}

/**
 * 转换时区
 * @param date 日期
 * @param timezone 目标时区
 * @returns 转换后的日期
 */
export const convertTimezone = (date: string | Date | dayjs.Dayjs, timezone: string): dayjs.Dayjs => {
  return dayjs(date).tz(timezone)
}

export default dayjs
