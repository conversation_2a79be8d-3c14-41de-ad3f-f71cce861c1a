<template>
  <div class="simple-login">
    <div class="login-container">
      <h1>她的日记 - 登录</h1>
      <el-form :model="form" class="login-form">
        <el-form-item>
          <el-input v-model="form.username" placeholder="用户名" />
        </el-form-item>
        <el-form-item>
          <el-input v-model="form.password" type="password" placeholder="密码" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleLogin">登录</el-button>
        </el-form-item>
      </el-form>
      <p>
        还没有账户？
        <router-link to="/auth/register">立即注册</router-link>
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores'
import { ElMessage } from 'element-plus'

const router = useRouter()
const authStore = useAuthStore()

const form = reactive({
  username: '',
  password: ''
})

const handleLogin = () => {
  if (!form.username || !form.password) {
    ElMessage.warning('请输入用户名和密码')
    return
  }

  // 模拟登录成功，设置认证状态
  const mockUser = {
    id: 1,
    username: form.username,
    email: `${form.username}@example.com`,
    nickname: form.username,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }

  // 直接设置localStorage来模拟登录
  localStorage.setItem('auth_token', 'mock-token-123')
  localStorage.setItem('auth_user', JSON.stringify(mockUser))

  // 更新store状态
  authStore.updateUserInfo(mockUser)

  ElMessage.success('登录成功')
  router.push('/dashboard')
}
</script>

<style scoped>
.simple-login {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #C8A2C8 0%, #FFB6C1 100%);
}

.login-container {
  background: white;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 400px;
}

h1 {
  text-align: center;
  margin-bottom: 2rem;
  color: #303133;
}

.login-form {
  margin-bottom: 1rem;
}

p {
  text-align: center;
  color: #606266;
}

a {
  color: #C8A2C8;
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}
</style>
