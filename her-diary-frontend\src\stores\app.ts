import { defineStore } from 'pinia'
import { ref, computed, readonly } from 'vue'

// 主题类型
export type Theme = 'light' | 'dark' | 'auto'

// 语言类型
export type Language = 'zh-CN' | 'en-US'

// 布局类型
export type Layout = 'default' | 'compact' | 'wide'

// 侧边栏状态
export type SidebarStatus = 'opened' | 'closed'

export const useAppStore = defineStore('app', () => {
  // 状态
  const theme = ref<Theme>('light')
  const language = ref<Language>('zh-CN')
  const layout = ref<Layout>('default')
  const sidebarStatus = ref<SidebarStatus>('opened')
  const loading = ref(false)
  const device = ref<'desktop' | 'tablet' | 'mobile'>('desktop')
  const pageTitle = ref('')
  const breadcrumbs = ref<{ name: string; path?: string }[]>([])

  // 设置相关
  const settings = ref({
    showBreadcrumb: true,
    showTagsView: true,
    showSidebarLogo: true,
    fixedHeader: true,
    sidebarTextTheme: true,
    showThemeSwitch: true,
    showLanguageSwitch: true,
    showFullScreen: true,
    showNotification: true,
    showSearch: true
  })

  // 计算属性
  const isMobile = computed(() => device.value === 'mobile')
  const isTablet = computed(() => device.value === 'tablet')
  const isDesktop = computed(() => device.value === 'desktop')
  const sidebarOpened = computed(() => sidebarStatus.value === 'opened')
  const isDarkTheme = computed(() => theme.value === 'dark')

  // 初始化：从localStorage恢复状态
  const initApp = () => {
    const savedTheme = localStorage.getItem('app_theme') as Theme
    const savedLanguage = localStorage.getItem('app_language') as Language
    const savedLayout = localStorage.getItem('app_layout') as Layout
    const savedSidebarStatus = localStorage.getItem('app_sidebar_status') as SidebarStatus
    const savedSettings = localStorage.getItem('app_settings')

    if (savedTheme) {
      theme.value = savedTheme
      applyTheme(savedTheme)
    }

    if (savedLanguage) {
      language.value = savedLanguage
    }

    if (savedLayout) {
      layout.value = savedLayout
    }

    if (savedSidebarStatus) {
      sidebarStatus.value = savedSidebarStatus
    }

    if (savedSettings) {
      try {
        settings.value = { ...settings.value, ...JSON.parse(savedSettings) }
      } catch (error) {
        console.error('解析设置失败:', error)
      }
    }

    // 检测设备类型
    detectDevice()
    
    // 监听窗口大小变化
    window.addEventListener('resize', detectDevice)
  }

  // 检测设备类型
  const detectDevice = () => {
    const width = window.innerWidth
    if (width < 768) {
      device.value = 'mobile'
      // 移动端默认关闭侧边栏
      if (sidebarStatus.value === 'opened') {
        toggleSidebar()
      }
    } else if (width < 1024) {
      device.value = 'tablet'
    } else {
      device.value = 'desktop'
    }
  }

  // 应用主题
  const applyTheme = (newTheme: Theme) => {
    const html = document.documentElement
    
    if (newTheme === 'auto') {
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
      html.setAttribute('data-theme', prefersDark ? 'dark' : 'light')
    } else {
      html.setAttribute('data-theme', newTheme)
    }
  }

  // 设置主题
  const setTheme = (newTheme: Theme) => {
    theme.value = newTheme
    applyTheme(newTheme)
    localStorage.setItem('app_theme', newTheme)
  }

  // 切换主题
  const toggleTheme = () => {
    const newTheme = theme.value === 'light' ? 'dark' : 'light'
    setTheme(newTheme)
  }

  // 设置语言
  const setLanguage = (newLanguage: Language) => {
    language.value = newLanguage
    localStorage.setItem('app_language', newLanguage)
    // 这里可以添加国际化逻辑
  }

  // 设置布局
  const setLayout = (newLayout: Layout) => {
    layout.value = newLayout
    localStorage.setItem('app_layout', newLayout)
  }

  // 切换侧边栏
  const toggleSidebar = () => {
    sidebarStatus.value = sidebarStatus.value === 'opened' ? 'closed' : 'opened'
    localStorage.setItem('app_sidebar_status', sidebarStatus.value)
  }

  // 设置侧边栏状态
  const setSidebarStatus = (status: SidebarStatus) => {
    sidebarStatus.value = status
    localStorage.setItem('app_sidebar_status', status)
  }

  // 设置加载状态
  const setLoading = (isLoading: boolean) => {
    loading.value = isLoading
  }

  // 设置页面标题
  const setPageTitle = (title: string) => {
    pageTitle.value = title
    document.title = title ? `${title} - 她的日记` : '她的日记'
  }

  // 设置面包屑
  const setBreadcrumbs = (crumbs: { name: string; path?: string }[]) => {
    breadcrumbs.value = crumbs
  }

  // 更新设置
  const updateSettings = (newSettings: Partial<typeof settings.value>) => {
    settings.value = { ...settings.value, ...newSettings }
    localStorage.setItem('app_settings', JSON.stringify(settings.value))
  }

  // 重置设置
  const resetSettings = () => {
    settings.value = {
      showBreadcrumb: true,
      showTagsView: true,
      showSidebarLogo: true,
      fixedHeader: true,
      sidebarTextTheme: true,
      showThemeSwitch: true,
      showLanguageSwitch: true,
      showFullScreen: true,
      showNotification: true,
      showSearch: true
    }
    localStorage.setItem('app_settings', JSON.stringify(settings.value))
  }

  // 进入全屏
  const enterFullscreen = () => {
    if (document.documentElement.requestFullscreen) {
      document.documentElement.requestFullscreen()
    }
  }

  // 退出全屏
  const exitFullscreen = () => {
    if (document.exitFullscreen) {
      document.exitFullscreen()
    }
  }

  // 切换全屏
  const toggleFullscreen = () => {
    if (document.fullscreenElement) {
      exitFullscreen()
    } else {
      enterFullscreen()
    }
  }

  // 初始化应用状态
  initApp()

  return {
    // 状态
    theme: readonly(theme),
    language: readonly(language),
    layout: readonly(layout),
    sidebarStatus: readonly(sidebarStatus),
    loading: readonly(loading),
    device: readonly(device),
    pageTitle: readonly(pageTitle),
    breadcrumbs: readonly(breadcrumbs),
    settings: readonly(settings),

    // 计算属性
    isMobile,
    isTablet,
    isDesktop,
    sidebarOpened,
    isDarkTheme,

    // 方法
    setTheme,
    toggleTheme,
    setLanguage,
    setLayout,
    toggleSidebar,
    setSidebarStatus,
    setLoading,
    setPageTitle,
    setBreadcrumbs,
    updateSettings,
    resetSettings,
    enterFullscreen,
    exitFullscreen,
    toggleFullscreen,
    detectDevice
  }
})
