<template>
  <div class="diary-edit-view">
    <div class="page-header">
      <h1 class="page-title">{{ isEdit ? '编辑日记' : '写日记' }}</h1>
      <div class="header-actions">
        <el-button @click="goBack">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSave">
          {{ loading ? '保存中...' : '保存' }}
        </el-button>
      </div>
    </div>

    <div class="page-content">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="80px"
        class="diary-form"
      >
        <el-form-item label="标题" prop="title">
          <el-input
            v-model="form.title"
            placeholder="请输入日记标题"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="内容" prop="content">
          <el-input
            v-model="form.content"
            type="textarea"
            placeholder="记录今天的心情和想法..."
            :rows="15"
            maxlength="10000"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="心情">
          <div class="mood-selector">
            <div
              v-for="mood in MOODS"
              :key="mood.value"
              class="mood-item"
              :class="{ active: form.mood === mood.value }"
              @click="form.mood = mood.value"
            >
              <span class="mood-icon">{{ mood.icon }}</span>
              <span class="mood-label">{{ mood.label }}</span>
            </div>
          </div>
        </el-form-item>

        <el-form-item label="天气">
          <div class="weather-selector">
            <div
              v-for="weather in WEATHER"
              :key="weather.value"
              class="weather-item"
              :class="{ active: form.weather === weather.value }"
              @click="form.weather = weather.value"
            >
              <span class="weather-icon">{{ weather.icon }}</span>
              <span class="weather-label">{{ weather.label }}</span>
            </div>
          </div>
        </el-form-item>

        <el-form-item label="标签">
          <el-select
            v-model="form.tagIds"
            multiple
            filterable
            allow-create
            placeholder="选择或创建标签"
            style="width: 100%"
          >
            <el-option
              v-for="tag in availableTags"
              :key="tag.id"
              :label="tag.name"
              :value="tag.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="公开">
          <el-switch
            v-model="form.is_public"
            active-text="公开"
            inactive-text="私密"
          />
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useDiaryStore } from '@/stores'
import { DIARY } from '@/utils/constants'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'

const router = useRouter()
const route = useRoute()
const diaryStore = useDiaryStore()

// 表单引用
const formRef = ref<FormInstance>()

// 响应式数据
const form = reactive({
  title: '',
  content: '',
  mood: '',
  weather: '',
  is_public: false,
  tagIds: [] as number[]
})

const availableTags = ref([
  { id: 1, name: '生活', color: '#FF6B6B' },
  { id: 2, name: '工作', color: '#4ECDC4' },
  { id: 3, name: '学习', color: '#45B7D1' },
  { id: 4, name: '旅行', color: '#96CEB4' },
  { id: 5, name: '感悟', color: '#FFEAA7' }
])

// 计算属性
const isEdit = computed(() => !!route.params.id)
const loading = computed(() => diaryStore.loading)
const MOODS = DIARY.MOODS
const WEATHER = DIARY.WEATHER

// 表单验证规则
const rules: FormRules = {
  title: [
    { required: true, message: '请输入日记标题', trigger: 'blur' },
    { min: 1, max: 100, message: '标题长度在 1 到 100 个字符', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入日记内容', trigger: 'blur' },
    { min: 1, max: 10000, message: '内容长度在 1 到 10000 个字符', trigger: 'blur' }
  ]
}

// 方法
const goBack = () => {
  router.back()
}

const handleSave = async () => {
  if (!formRef.value) return

  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    let success = false
    if (isEdit.value) {
      success = await diaryStore.updateDiary(Number(route.params.id), form)
    } else {
      success = await diaryStore.createDiary(form)
    }

    if (success) {
      ElMessage.success(isEdit.value ? '日记更新成功' : '日记创建成功')
      router.push('/diary/list')
    }
  } catch (error) {
    console.error('保存日记失败:', error)
  }
}

const loadDiary = async () => {
  if (isEdit.value) {
    const diary = await diaryStore.fetchDiaryDetail(Number(route.params.id))
    if (diary) {
      form.title = diary.title
      form.content = diary.content
      form.mood = diary.mood || ''
      form.weather = diary.weather || ''
      form.is_public = diary.is_public
      form.tagIds = diary.tags?.map(tag => tag.id) || []
    }
  }
}

// 生命周期
onMounted(() => {
  loadDiary()
})
</script>

<style lang="scss" scoped>
@import '@/assets/styles/variables.scss';

.diary-edit-view {
  max-width: 800px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-2xl);

  .page-title {
    font-size: var(--font-size-3xl);
    font-weight: bold;
    color: var(--text-primary);
    margin: 0;
  }

  .header-actions {
    display: flex;
    gap: var(--spacing-sm);
  }
}

.diary-form {
  background: var(--surface-color);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-2xl);
  box-shadow: var(--shadow-md);

  :deep(.el-form-item__label) {
    font-weight: 500;
    color: var(--text-primary);
  }

  :deep(.el-textarea__inner) {
    font-family: var(--font-family-primary);
    line-height: var(--line-height-loose);
  }
}

.mood-selector,
.weather-selector {
  display: flex;
  gap: var(--spacing-sm);
  flex-wrap: wrap;
}

.mood-item,
.weather-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm);
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius-md);
  cursor: pointer;
  transition: var(--transition-fast);
  min-width: 60px;

  &:hover {
    border-color: var(--primary-color);
    background: var(--primary-light);
  }

  &.active {
    border-color: var(--primary-color);
    background: var(--primary-color);
    color: white;
  }

  .mood-icon,
  .weather-icon {
    font-size: 20px;
  }

  .mood-label,
  .weather-label {
    font-size: var(--font-size-xs);
    text-align: center;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-md);

    .header-actions {
      width: 100%;
      justify-content: flex-end;
    }
  }

  .diary-form {
    padding: var(--spacing-lg);
  }

  .mood-selector,
  .weather-selector {
    gap: var(--spacing-xs);
  }

  .mood-item,
  .weather-item {
    min-width: 50px;
    padding: var(--spacing-xs);

    .mood-icon,
    .weather-icon {
      font-size: 16px;
    }

    .mood-label,
    .weather-label {
      font-size: 10px;
    }
  }
}
</style>
