<template>
  <div class="register-view">
    <div class="register-container">
      <!-- 左侧装饰区域 -->
      <div class="register-decoration">
        <div class="decoration-content">
          <h1 class="decoration-title">加入我们</h1>
          <p class="decoration-subtitle">开始您的日记之旅</p>
          <div class="decoration-steps">
            <div class="step-item">
              <div class="step-number">1</div>
              <div class="step-content">
                <h3>创建账户</h3>
                <p>填写基本信息，快速注册</p>
              </div>
            </div>
            <div class="step-item">
              <div class="step-number">2</div>
              <div class="step-content">
                <h3>开始写作</h3>
                <p>记录生活中的点点滴滴</p>
              </div>
            </div>
            <div class="step-item">
              <div class="step-number">3</div>
              <div class="step-content">
                <h3>回顾成长</h3>
                <p>查看统计，见证自己的成长</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧注册表单 -->
      <div class="register-form-container">
        <div class="register-form">
          <div class="form-header">
            <h2 class="form-title">创建账户</h2>
            <p class="form-subtitle">请填写以下信息完成注册</p>
          </div>

          <el-form
            ref="registerFormRef"
            :model="registerForm"
            :rules="registerRules"
            class="register-form-content"
            @submit.prevent="handleRegister"
          >
            <el-form-item prop="username">
              <el-input
                v-model="registerForm.username"
                placeholder="请输入用户名"
                size="large"
                clearable
              >
                <template #prefix>
                  <el-icon><User /></el-icon>
                </template>
              </el-input>
            </el-form-item>

            <el-form-item prop="email">
              <el-input
                v-model="registerForm.email"
                placeholder="请输入邮箱地址"
                size="large"
                clearable
              >
                <template #prefix>
                  <el-icon><Message /></el-icon>
                </template>
              </el-input>
            </el-form-item>

            <el-form-item prop="nickname">
              <el-input
                v-model="registerForm.nickname"
                placeholder="请输入昵称（可选）"
                size="large"
                clearable
              >
                <template #prefix>
                  <el-icon><UserFilled /></el-icon>
                </template>
              </el-input>
            </el-form-item>

            <el-form-item prop="password">
              <el-input
                v-model="registerForm.password"
                type="password"
                placeholder="请输入密码"
                size="large"
                show-password
                clearable
              >
                <template #prefix>
                  <el-icon><Lock /></el-icon>
                </template>
              </el-input>
            </el-form-item>

            <el-form-item prop="confirmPassword">
              <el-input
                v-model="registerForm.confirmPassword"
                type="password"
                placeholder="请确认密码"
                size="large"
                show-password
                clearable
                @keyup.enter="handleRegister"
              >
                <template #prefix>
                  <el-icon><Lock /></el-icon>
                </template>
              </el-input>
            </el-form-item>

            <div class="form-agreement">
              <el-checkbox v-model="agreeTerms" size="large">
                我已阅读并同意
                <el-link type="primary" @click="showTerms">《服务条款》</el-link>
                和
                <el-link type="primary" @click="showPrivacy">《隐私政策》</el-link>
              </el-checkbox>
            </div>

            <el-button
              type="primary"
              size="large"
              class="register-button"
              :loading="loading"
              :disabled="!agreeTerms"
              @click="handleRegister"
            >
              {{ loading ? '注册中...' : '立即注册' }}
            </el-button>
          </el-form>

          <div class="form-footer">
            <p class="login-tip">
              已有账户？
              <router-link to="/auth/login" class="login-link">
                立即登录
              </router-link>
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import {
  User,
  UserFilled,
  Lock,
  Message
} from '@element-plus/icons-vue'

const router = useRouter()
const authStore = useAuthStore()

// 表单引用
const registerFormRef = ref<FormInstance>()

// 响应式数据
const registerForm = reactive({
  username: '',
  email: '',
  nickname: '',
  password: '',
  confirmPassword: ''
})

const agreeTerms = ref(false)

// 计算属性
const loading = computed(() => authStore.loading)

// 自定义验证器
const validateConfirmPassword = (rule: any, value: string, callback: any) => {
  if (value === '') {
    callback(new Error('请确认密码'))
  } else if (value !== registerForm.password) {
    callback(new Error('两次输入的密码不一致'))
  } else {
    callback()
  }
}

// 表单验证规则
const registerRules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  nickname: [
    { max: 20, message: '昵称长度不能超过 20 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' },
    { pattern: /^(?=.*[a-zA-Z])(?=.*\d)/, message: '密码必须包含字母和数字', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, validator: validateConfirmPassword, trigger: 'blur' }
  ]
}

// 方法
const handleRegister = async () => {
  if (!registerFormRef.value) return

  try {
    const valid = await registerFormRef.value.validate()
    if (!valid) return

    if (!agreeTerms.value) {
      ElMessage.warning('请先同意服务条款和隐私政策')
      return
    }

    const success = await authStore.register(registerForm)
    if (success) {
      ElMessage.success('注册成功，请登录')
      router.push('/auth/login')
    }
  } catch (error) {
    console.error('注册失败:', error)
  }
}

const showTerms = () => {
  ElMessage.info('服务条款页面开发中...')
}

const showPrivacy = () => {
  ElMessage.info('隐私政策页面开发中...')
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/variables.scss';

.register-view {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--accent-light) 0%, var(--primary-light) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-lg);
}

.register-container {
  width: 100%;
  max-width: 1000px;
  background: var(--surface-color);
  border-radius: var(--border-radius-xl);
  box-shadow: var(--shadow-xl);
  overflow: hidden;
  display: flex;
  min-height: 700px;
}

.register-decoration {
  flex: 1;
  background: var(--gradient-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-2xl);
  color: white;

  .decoration-content {
    text-align: center;
    max-width: 400px;
  }

  .decoration-title {
    font-size: var(--font-size-3xl);
    font-weight: bold;
    margin: 0 0 var(--spacing-md) 0;
  }

  .decoration-subtitle {
    font-size: var(--font-size-lg);
    margin: 0 0 var(--spacing-2xl) 0;
    opacity: 0.9;
  }

  .decoration-steps {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
  }

  .step-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    text-align: left;

    .step-number {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.2);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: var(--font-size-lg);
      font-weight: bold;
      flex-shrink: 0;
    }

    .step-content {
      h3 {
        font-size: var(--font-size-base);
        margin: 0 0 var(--spacing-xs) 0;
      }

      p {
        font-size: var(--font-size-sm);
        margin: 0;
        opacity: 0.8;
      }
    }
  }
}

.register-form-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-2xl);
}

.register-form {
  width: 100%;
  max-width: 400px;

  .form-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);

    .form-title {
      font-size: var(--font-size-2xl);
      font-weight: bold;
      color: var(--text-primary);
      margin: 0 0 var(--spacing-sm) 0;
    }

    .form-subtitle {
      font-size: var(--font-size-base);
      color: var(--text-secondary);
      margin: 0;
    }
  }

  .register-form-content {
    .el-form-item {
      margin-bottom: var(--spacing-md);
    }
  }

  .form-agreement {
    margin-bottom: var(--spacing-lg);
    
    :deep(.el-checkbox__label) {
      font-size: var(--font-size-sm);
      color: var(--text-secondary);
    }
  }

  .register-button {
    width: 100%;
    height: 48px;
    font-size: var(--font-size-base);
    font-weight: 500;
  }

  .form-footer {
    text-align: center;
    margin-top: var(--spacing-lg);

    .login-tip {
      font-size: var(--font-size-sm);
      color: var(--text-secondary);
      margin: 0;

      .login-link {
        color: var(--primary-color);
        text-decoration: none;
        font-weight: 500;

        &:hover {
          text-decoration: underline;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .register-view {
    padding: var(--spacing-md);
  }

  .register-container {
    flex-direction: column;
    min-height: auto;
  }

  .register-decoration {
    padding: var(--spacing-xl) var(--spacing-lg);

    .decoration-title {
      font-size: var(--font-size-2xl);
    }

    .decoration-steps {
      gap: var(--spacing-lg);

      .step-item {
        .step-number {
          width: 32px;
          height: 32px;
          font-size: var(--font-size-base);
        }
      }
    }
  }

  .register-form-container {
    padding: var(--spacing-xl) var(--spacing-lg);
  }
}
</style>
