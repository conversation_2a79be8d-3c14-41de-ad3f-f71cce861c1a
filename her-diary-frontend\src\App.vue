<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { useAppStore } from '@/stores'
import AppHeader from '@/components/common/AppHeader.vue'
import AppSidebar from '@/components/common/AppSidebar.vue'
import AppFooter from '@/components/common/AppFooter.vue'

const route = useRoute()
const appStore = useAppStore()

// 计算属性
const layout = computed(() => route.meta?.layout || 'default')
const sidebarOpened = computed(() => appStore.sidebarOpened)
const isMobile = computed(() => appStore.isMobile)
</script>

<template>
  <div id="app" class="app-container" :class="{ 'sidebar-collapsed': !sidebarOpened, 'is-mobile': isMobile }">
    <!-- 认证布局 -->
    <template v-if="layout === 'auth'">
      <router-view />
    </template>

    <!-- 空白布局 -->
    <template v-else-if="layout === 'blank'">
      <router-view />
    </template>

    <!-- 默认布局 -->
    <template v-else>
      <!-- 侧边栏 -->
      <AppSidebar />

      <!-- 主要内容区域 -->
      <div class="main-container">
        <!-- 顶部导航 -->
        <AppHeader />

        <!-- 页面内容 -->
        <main class="main-content">
          <router-view />
        </main>

        <!-- 底部 -->
        <AppFooter />
      </div>

      <!-- 移动端遮罩 -->
      <div
        v-if="isMobile && sidebarOpened"
        class="sidebar-overlay"
        @click="appStore.setSidebarStatus('closed')"
      ></div>
    </template>
  </div>
</template>

<style lang="scss" scoped>
@import '@/assets/styles/variables.scss';

.app-container {
  display: flex;
  min-height: 100vh;
  background: var(--background-color);

  &.is-mobile {
    .main-container {
      margin-left: 0;
    }
  }

  &.sidebar-collapsed:not(.is-mobile) {
    .main-container {
      margin-left: 64px;
    }
  }
}

.main-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-left: 250px;
  transition: margin-left var(--transition-normal);
  min-height: 100vh;
}

.main-content {
  flex: 1;
  padding: var(--spacing-lg);
  overflow-y: auto;

  @media (max-width: 768px) {
    padding: var(--spacing-md);
  }
}

.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: calc(var(--z-fixed) - 1);
}

// 全局滚动条样式
:deep(*) {
  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  &::-webkit-scrollbar-track {
    background: var(--background-color);
    border-radius: var(--border-radius-sm);
  }

  &::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: var(--border-radius-sm);
    transition: var(--transition-fast);

    &:hover {
      background: var(--text-placeholder);
    }
  }
}
</style>
