import { createRouter, createWebHistory, type RouteRecordRaw } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { ElMessage } from 'element-plus'

// 路由元信息接口
declare module 'vue-router' {
  interface RouteMeta {
    title?: string
    requiresAuth?: boolean
    requiresGuest?: boolean
    layout?: 'default' | 'auth' | 'blank'
    icon?: string
    hidden?: boolean
    keepAlive?: boolean
  }
}

// 路由配置
const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/auth',
    name: 'Auth',
    redirect: '/auth/login',
    meta: {
      layout: 'auth',
      requiresGuest: true
    },
    children: [
      {
        path: 'login',
        name: 'Login',
        component: () => import('@/views/auth/SimpleLoginView.vue'),
        meta: {
          title: '登录',
          requiresGuest: true
        }
      },
      {
        path: 'register',
        name: 'Register',
        component: () => import('@/views/auth/RegisterView.vue'),
        meta: {
          title: '注册',
          requiresGuest: true
        }
      }
    ]
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/views/dashboard/DashboardView.vue'),
    meta: {
      title: '仪表板',
      requiresAuth: true,
      icon: 'Dashboard',
      keepAlive: true
    }
  },
  {
    path: '/diary',
    name: 'DiaryLayout',
    redirect: '/diary/list',
    meta: {
      title: '日记管理',
      requiresAuth: true,
      icon: 'Document'
    },
    children: [
      {
        path: 'list',
        name: 'DiaryList',
        component: () => import('@/views/diary/DiaryListView.vue'),
        meta: {
          title: '日记列表',
          requiresAuth: true,
          keepAlive: true
        }
      },
      {
        path: 'create',
        name: 'DiaryCreate',
        component: () => import('@/views/diary/DiaryEditView.vue'),
        meta: {
          title: '写日记',
          requiresAuth: true
        }
      },
      {
        path: 'edit/:id',
        name: 'DiaryEdit',
        component: () => import('@/views/diary/DiaryEditView.vue'),
        meta: {
          title: '编辑日记',
          requiresAuth: true
        }
      },
      {
        path: 'detail/:id',
        name: 'DiaryDetail',
        component: () => import('@/views/diary/DiaryDetailView.vue'),
        meta: {
          title: '日记详情',
          requiresAuth: true
        }
      }
    ]
  },
  {
    path: '/user',
    name: 'UserLayout',
    redirect: '/user/profile',
    meta: {
      title: '个人中心',
      requiresAuth: true,
      icon: 'User'
    },
    children: [
      {
        path: 'profile',
        name: 'UserProfile',
        component: () => import('@/views/user/ProfileView.vue'),
        meta: {
          title: '个人资料',
          requiresAuth: true
        }
      }
    ]
  },
  {
    path: '/404',
    name: 'NotFound',
    component: () => import('@/views/error/NotFoundView.vue'),
    meta: {
      title: '页面不存在',
      layout: 'blank',
      hidden: true
    }
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404'
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 全局前置守卫
router.beforeEach(async (to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 她的日记`
  } else {
    document.title = '她的日记'
  }

  const authStore = useAuthStore()
  
  // 检查是否需要认证
  if (to.meta.requiresAuth) {
    if (!authStore.isAuthenticated) {
      ElMessage.warning('请先登录')
      next({
        name: 'Login',
        query: { redirect: to.fullPath }
      })
      return
    }
    
    // 如果已登录但用户信息为空，尝试获取用户信息
    if (!authStore.user) {
      try {
        await authStore.fetchUserInfo()
      } catch (error) {
        console.error('获取用户信息失败:', error)
        authStore.logout()
        next({
          name: 'Login',
          query: { redirect: to.fullPath }
        })
        return
      }
    }
  }
  
  // 检查是否需要游客状态（未登录）
  if (to.meta.requiresGuest && authStore.isAuthenticated) {
    next({ name: 'Dashboard' })
    return
  }
  
  next()
})

// 全局后置钩子
router.afterEach((to, from) => {
  // 可以在这里添加页面访问统计等逻辑
  console.log(`路由跳转: ${from.path} -> ${to.path}`)
})

// 路由错误处理
router.onError((error) => {
  console.error('路由错误:', error)
  ElMessage.error('页面加载失败，请刷新重试')
})

export default router

// 导出路由配置供其他地方使用
export { routes }
