import { defineStore } from 'pinia'
import { ref, computed, readonly } from 'vue'
import { diary<PERSON>pi, type Diary, type DiaryListParams, type DiaryFormData } from '@/api'
import { ElMessage } from 'element-plus'

export const useDiaryStore = defineStore('diary', () => {
  // 状态
  const diaries = ref<Diary[]>([])
  const currentDiary = ref<Diary | null>(null)
  const loading = ref(false)
  const listLoading = ref(false)
  const total = ref(0)
  const currentPage = ref(1)
  const pageSize = ref(10)
  const searchParams = ref<DiaryListParams>({})

  // 计算属性
  const totalPages = computed(() => Math.ceil(total.value / pageSize.value))
  const hasMore = computed(() => currentPage.value < totalPages.value)
  const isEmpty = computed(() => diaries.value.length === 0)

  // 获取日记列表
  const fetchDiaries = async (params: DiaryListParams = {}) => {
    try {
      listLoading.value = true
      searchParams.value = { ...searchParams.value, ...params }
      
      const response = await diaryApi.getList({
        page: currentPage.value,
        pageSize: pageSize.value,
        ...searchParams.value
      })
      
      if (response.code === 200) {
        diaries.value = response.data.data
        total.value = response.data.total
        currentPage.value = response.data.page
        pageSize.value = response.data.pageSize
        return response.data
      }
      return null
    } catch (error) {
      console.error('获取日记列表失败:', error)
      return null
    } finally {
      listLoading.value = false
    }
  }

  // 加载更多日记（分页加载）
  const loadMoreDiaries = async () => {
    if (!hasMore.value || listLoading.value) return
    
    try {
      listLoading.value = true
      const nextPage = currentPage.value + 1
      
      const response = await diaryApi.getList({
        page: nextPage,
        pageSize: pageSize.value,
        ...searchParams.value
      })
      
      if (response.code === 200) {
        diaries.value.push(...response.data.data)
        currentPage.value = response.data.page
        return response.data
      }
      return null
    } catch (error) {
      console.error('加载更多日记失败:', error)
      return null
    } finally {
      listLoading.value = false
    }
  }

  // 获取日记详情
  const fetchDiaryDetail = async (id: number) => {
    try {
      loading.value = true
      const response = await diaryApi.getDetail(id)
      
      if (response.code === 200) {
        currentDiary.value = response.data
        return response.data
      }
      return null
    } catch (error) {
      console.error('获取日记详情失败:', error)
      return null
    } finally {
      loading.value = false
    }
  }

  // 创建日记
  const createDiary = async (data: DiaryFormData) => {
    try {
      loading.value = true
      const response = await diaryApi.create(data)
      
      if (response.code === 200) {
        // 将新日记添加到列表开头
        diaries.value.unshift(response.data)
        total.value += 1
        ElMessage.success('日记创建成功')
        return response.data
      }
      return null
    } catch (error) {
      console.error('创建日记失败:', error)
      return null
    } finally {
      loading.value = false
    }
  }

  // 更新日记
  const updateDiary = async (id: number, data: DiaryFormData) => {
    try {
      loading.value = true
      const response = await diaryApi.update(id, data)
      
      if (response.code === 200) {
        // 更新列表中的日记
        const index = diaries.value.findIndex(diary => diary.id === id)
        if (index !== -1) {
          diaries.value[index] = response.data
        }
        
        // 更新当前日记
        if (currentDiary.value?.id === id) {
          currentDiary.value = response.data
        }
        
        ElMessage.success('日记更新成功')
        return response.data
      }
      return null
    } catch (error) {
      console.error('更新日记失败:', error)
      return null
    } finally {
      loading.value = false
    }
  }

  // 删除日记
  const deleteDiary = async (id: number) => {
    try {
      loading.value = true
      const response = await diaryApi.delete(id)
      
      if (response.code === 200) {
        // 从列表中移除日记
        const index = diaries.value.findIndex(diary => diary.id === id)
        if (index !== -1) {
          diaries.value.splice(index, 1)
          total.value -= 1
        }
        
        // 清除当前日记
        if (currentDiary.value?.id === id) {
          currentDiary.value = null
        }
        
        ElMessage.success('日记删除成功')
        return true
      }
      return false
    } catch (error) {
      console.error('删除日记失败:', error)
      return false
    } finally {
      loading.value = false
    }
  }

  // 批量删除日记
  const batchDeleteDiaries = async (ids: number[]) => {
    try {
      loading.value = true
      const response = await diaryApi.batchDelete(ids)
      
      if (response.code === 200) {
        // 从列表中移除日记
        diaries.value = diaries.value.filter(diary => !ids.includes(diary.id))
        total.value -= ids.length
        
        ElMessage.success(`成功删除 ${ids.length} 篇日记`)
        return true
      }
      return false
    } catch (error) {
      console.error('批量删除日记失败:', error)
      return false
    } finally {
      loading.value = false
    }
  }

  // 搜索日记
  const searchDiaries = async (keyword: string, params: Partial<DiaryListParams> = {}) => {
    try {
      listLoading.value = true
      const response = await diaryApi.search(keyword, {
        page: 1,
        pageSize: pageSize.value,
        ...params
      })
      
      if (response.code === 200) {
        diaries.value = response.data.data
        total.value = response.data.total
        currentPage.value = 1
        searchParams.value = { keyword, ...params }
        return response.data
      }
      return null
    } catch (error) {
      console.error('搜索日记失败:', error)
      return null
    } finally {
      listLoading.value = false
    }
  }

  // 获取随机日记
  const fetchRandomDiary = async () => {
    try {
      const response = await diaryApi.getRandom()
      
      if (response.code === 200) {
        return response.data
      }
      return null
    } catch (error) {
      console.error('获取随机日记失败:', error)
      return null
    }
  }

  // 上传图片
  const uploadImage = async (file: File) => {
    try {
      const response = await diaryApi.uploadImage(file)
      
      if (response.code === 200) {
        return response.data.url
      }
      return null
    } catch (error) {
      console.error('上传图片失败:', error)
      return null
    }
  }

  // 重置状态
  const resetState = () => {
    diaries.value = []
    currentDiary.value = null
    total.value = 0
    currentPage.value = 1
    searchParams.value = {}
  }

  // 设置当前页
  const setCurrentPage = (page: number) => {
    currentPage.value = page
  }

  // 设置页面大小
  const setPageSize = (size: number) => {
    pageSize.value = size
    currentPage.value = 1
  }

  // 清除当前日记
  const clearCurrentDiary = () => {
    currentDiary.value = null
  }

  return {
    // 状态
    diaries: readonly(diaries),
    currentDiary: readonly(currentDiary),
    loading: readonly(loading),
    listLoading: readonly(listLoading),
    total: readonly(total),
    currentPage: readonly(currentPage),
    pageSize: readonly(pageSize),
    searchParams: readonly(searchParams),
    
    // 计算属性
    totalPages,
    hasMore,
    isEmpty,
    
    // 方法
    fetchDiaries,
    loadMoreDiaries,
    fetchDiaryDetail,
    createDiary,
    updateDiary,
    deleteDiary,
    batchDeleteDiaries,
    searchDiaries,
    fetchRandomDiary,
    uploadImage,
    resetState,
    setCurrentPage,
    setPageSize,
    clearCurrentDiary
  }
})
