<template>
  <aside class="app-sidebar" :class="{ 'is-collapsed': !sidebarOpened }">
    <!-- Logo区域 -->
    <div v-if="settings.showSidebarLogo" class="sidebar-logo">
      <router-link to="/" class="logo-link">
        <img src="/favicon.ico" alt="Logo" class="logo-image" />
        <span v-show="sidebarOpened" class="logo-text">她的日记</span>
      </router-link>
    </div>

    <!-- 导航菜单 -->
    <el-menu
      :default-active="activeMenu"
      :collapse="!sidebarOpened"
      :unique-opened="true"
      class="sidebar-menu"
      router
    >
      <!-- 仪表板 -->
      <el-menu-item index="/dashboard">
        <template #title>📊 仪表板</template>
      </el-menu-item>

      <!-- 日记管理 -->
      <el-sub-menu index="/diary">
        <template #title>
          <span>📝 日记管理</span>
        </template>
        <el-menu-item index="/diary/list">
          <template #title>📄 日记列表</template>
        </el-menu-item>
        <el-menu-item index="/diary/create">
          <template #title>✏️ 写日记</template>
        </el-menu-item>
      </el-sub-menu>

      <!-- 标签管理 -->
      <el-menu-item index="/tags">
        <template #title>🏷️ 标签管理</template>
      </el-menu-item>

      <!-- 统计分析 -->
      <el-menu-item index="/analytics">
        <template #title>📈 统计分析</template>
      </el-menu-item>

      <!-- 个人中心 -->
      <el-sub-menu index="/user">
        <template #title>
          <span>👤 个人中心</span>
        </template>
        <el-menu-item index="/user/profile">
          <template #title>👤 个人资料</template>
        </el-menu-item>
        <el-menu-item index="/user/settings">
          <template #title>⚙️ 账户设置</template>
        </el-menu-item>
      </el-sub-menu>
    </el-menu>

    <!-- 底部信息 -->
    <div v-show="sidebarOpened" class="sidebar-footer">
      <div class="footer-info">
        <p class="app-version">v1.0.0</p>
        <p class="copyright">© 2024 她的日记</p>
      </div>
    </div>
  </aside>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { useAppStore } from '@/stores'
// 图标已替换为emoji，暂时移除图标导入

const route = useRoute()
const appStore = useAppStore()

// 计算属性
const sidebarOpened = computed(() => appStore.sidebarOpened)
const settings = computed(() => appStore.settings)
const activeMenu = computed(() => {
  const { path } = route
  // 处理子路由的激活状态
  if (path.startsWith('/diary/')) {
    return '/diary/list'
  }
  if (path.startsWith('/user/')) {
    return path
  }
  return path
})
</script>

<style lang="scss" scoped>
@import '@/assets/styles/variables.scss';

.app-sidebar {
  width: 250px;
  height: 100vh;
  background: var(--surface-color);
  border-right: 1px solid var(--border-color);
  transition: width var(--transition-normal);
  overflow: hidden;
  display: flex;
  flex-direction: column;

  &.is-collapsed {
    width: 64px;
  }
}

.sidebar-logo {
  height: 60px;
  display: flex;
  align-items: center;
  padding: 0 var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);

  .logo-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    text-decoration: none;
    color: var(--text-primary);
    font-weight: 600;
    font-size: var(--font-size-lg);
  }

  .logo-image {
    width: 32px;
    height: 32px;
    flex-shrink: 0;
  }

  .logo-text {
    white-space: nowrap;
    transition: var(--transition-normal);
  }
}

.sidebar-menu {
  flex: 1;
  border: none;
  background: transparent;

  :deep(.el-menu-item) {
    height: 48px;
    line-height: 48px;
    margin: 0 var(--spacing-sm);
    border-radius: var(--border-radius-md);
    transition: var(--transition-fast);

    &:hover {
      background: var(--primary-light);
      color: var(--primary-color);
    }

    &.is-active {
      background: var(--primary-color);
      color: white;

      &::before {
        display: none;
      }
    }
  }

  :deep(.el-sub-menu) {
    .el-sub-menu__title {
      height: 48px;
      line-height: 48px;
      margin: 0 var(--spacing-sm);
      border-radius: var(--border-radius-md);
      transition: var(--transition-fast);

      &:hover {
        background: var(--primary-light);
        color: var(--primary-color);
      }
    }

    .el-menu {
      background: transparent;
    }

    .el-menu-item {
      margin: 0 var(--spacing-md);
      padding-left: 48px !important;
    }
  }

  :deep(.el-menu--collapse) {
    .el-menu-item,
    .el-sub-menu__title {
      margin: 0 var(--spacing-xs);
      text-align: center;
    }
  }
}

.sidebar-footer {
  padding: var(--spacing-md);
  border-top: 1px solid var(--border-color);
  background: var(--background-color);

  .footer-info {
    text-align: center;
  }

  .app-version {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    margin: 0 0 var(--spacing-xs) 0;
  }

  .copyright {
    font-size: var(--font-size-xs);
    color: var(--text-placeholder);
    margin: 0;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .app-sidebar {
    position: fixed;
    top: 0;
    left: 0;
    z-index: var(--z-fixed);
    transform: translateX(-100%);
    transition: transform var(--transition-normal);

    &:not(.is-collapsed) {
      transform: translateX(0);
    }
  }
}
</style>
