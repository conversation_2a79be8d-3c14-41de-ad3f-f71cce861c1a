<template>
  <div class="diary-list-view">
    <div class="page-header">
      <h1 class="page-title">我的日记</h1>
      <el-button type="primary" @click="goToCreate">
        <el-icon><EditPen /></el-icon>
        写日记
      </el-button>
    </div>

    <div class="page-content">
      <!-- 搜索和筛选 -->
      <div class="search-filters">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索日记..."
          clearable
          @keyup.enter="handleSearch"
          @clear="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>

      <!-- 日记列表 -->
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="5" animated />
      </div>
      
      <div v-else-if="diaries.length === 0" class="empty-state">
        <el-empty description="还没有日记，开始写第一篇吧！">
          <el-button type="primary" @click="goToCreate">写日记</el-button>
        </el-empty>
      </div>
      
      <div v-else class="diary-grid">
        <div
          v-for="diary in diaries"
          :key="diary.id"
          class="diary-card"
          @click="goToDetail(diary.id)"
        >
          <div class="card-header">
            <h3 class="card-title">{{ diary.title }}</h3>
            <span class="card-date">{{ smartFormatDate(diary.created_at) }}</span>
          </div>
          
          <p class="card-content">{{ truncateString(diary.content, 150) }}</p>
          
          <div v-if="diary.tags && diary.tags.length > 0" class="card-tags">
            <el-tag
              v-for="tag in diary.tags.slice(0, 3)"
              :key="tag.id"
              size="small"
              :color="tag.color"
            >
              {{ tag.name }}
            </el-tag>
          </div>
          
          <div class="card-actions">
            <el-button type="text" @click.stop="goToEdit(diary.id)">
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
            <el-button type="text" @click.stop="handleDelete(diary.id)">
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div v-if="total > 0" class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useDiaryStore } from '@/stores'
import { smartFormatDate, truncateString } from '@/utils'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  EditPen,
  Search,
  Edit,
  Delete
} from '@element-plus/icons-vue'

const router = useRouter()
const diaryStore = useDiaryStore()

// 响应式数据
const searchKeyword = ref('')

// 计算属性
const diaries = computed(() => diaryStore.diaries)
const loading = computed(() => diaryStore.listLoading)
const total = computed(() => diaryStore.total)
const currentPage = computed({
  get: () => diaryStore.currentPage,
  set: (value) => diaryStore.setCurrentPage(value)
})
const pageSize = computed({
  get: () => diaryStore.pageSize,
  set: (value) => diaryStore.setPageSize(value)
})

// 方法
const goToCreate = () => {
  router.push('/diary/create')
}

const goToDetail = (id: number) => {
  router.push(`/diary/detail/${id}`)
}

const goToEdit = (id: number) => {
  router.push(`/diary/edit/${id}`)
}

const handleSearch = () => {
  if (searchKeyword.value.trim()) {
    diaryStore.searchDiaries(searchKeyword.value.trim())
  } else {
    diaryStore.fetchDiaries()
  }
}

const handleDelete = async (id: number) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这篇日记吗？删除后无法恢复。',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const success = await diaryStore.deleteDiary(id)
    if (success) {
      ElMessage.success('日记删除成功')
    }
  } catch (error) {
    // 用户取消操作
  }
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  loadDiaries()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  loadDiaries()
}

const loadDiaries = () => {
  if (searchKeyword.value.trim()) {
    diaryStore.searchDiaries(searchKeyword.value.trim())
  } else {
    diaryStore.fetchDiaries()
  }
}

// 监听器
watch([currentPage, pageSize], () => {
  loadDiaries()
})

// 生命周期
onMounted(() => {
  loadDiaries()
})
</script>

<style lang="scss" scoped>
@import '@/assets/styles/variables.scss';

.diary-list-view {
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-2xl);

  .page-title {
    font-size: var(--font-size-3xl);
    font-weight: bold;
    color: var(--text-primary);
    margin: 0;
  }
}

.search-filters {
  margin-bottom: var(--spacing-xl);

  .el-input {
    max-width: 400px;
  }
}

.loading-container {
  background: var(--surface-color);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
}

.empty-state {
  background: var(--surface-color);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-2xl);
  text-align: center;
}

.diary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.diary-card {
  background: var(--surface-color);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-md);
  cursor: pointer;
  transition: var(--transition-normal);
  display: flex;
  flex-direction: column;
  height: 280px;

  &:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-md);

    .card-title {
      font-size: var(--font-size-lg);
      font-weight: 600;
      color: var(--text-primary);
      margin: 0;
      flex: 1;
      @include text-ellipsis(2);
    }

    .card-date {
      font-size: var(--font-size-sm);
      color: var(--text-secondary);
      white-space: nowrap;
      margin-left: var(--spacing-sm);
    }
  }

  .card-content {
    font-size: var(--font-size-base);
    color: var(--text-secondary);
    line-height: var(--line-height-loose);
    margin: 0 0 var(--spacing-md) 0;
    flex: 1;
    overflow: hidden;
  }

  .card-tags {
    display: flex;
    gap: var(--spacing-xs);
    flex-wrap: wrap;
    margin-bottom: var(--spacing-md);
  }

  .card-actions {
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-sm);
    padding-top: var(--spacing-sm);
    border-top: 1px solid var(--border-color);
  }
}

.pagination {
  display: flex;
  justify-content: center;
  margin-top: var(--spacing-xl);
}

// 响应式设计
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-md);
  }

  .diary-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  .diary-card {
    height: auto;
    min-height: 200px;
  }

  .search-filters {
    .el-input {
      max-width: 100%;
    }
  }
}
</style>
