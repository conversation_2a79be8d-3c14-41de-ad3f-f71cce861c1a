<template>
  <header class="app-header">
    <div class="app-header__left">
      <!-- 侧边栏切换按钮 -->
      <el-button
        type="text"
        class="sidebar-toggle"
        @click="toggleSidebar"
      >
        <el-icon :size="20">
          <Fold v-if="sidebarOpened" />
          <Expand v-else />
        </el-icon>
      </el-button>

      <!-- 面包屑导航 -->
      <el-breadcrumb
        v-if="settings.showBreadcrumb && breadcrumbs.length > 0"
        class="breadcrumb"
        separator="/"
      >
        <el-breadcrumb-item
          v-for="(item, index) in breadcrumbs"
          :key="index"
          :to="item.path"
        >
          {{ item.name }}
        </el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <div class="app-header__right">
      <!-- 搜索 -->
      <div v-if="settings.showSearch" class="header-item">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索日记..."
          class="search-input"
          clearable
          @keyup.enter="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>

      <!-- 全屏切换 -->
      <div v-if="settings.showFullScreen" class="header-item">
        <el-tooltip content="全屏" placement="bottom">
          <el-button type="text" @click="toggleFullscreen">
            <el-icon :size="18">
              <FullScreen />
            </el-icon>
          </el-button>
        </el-tooltip>
      </div>

      <!-- 主题切换 -->
      <div v-if="settings.showThemeSwitch" class="header-item">
        <el-tooltip :content="isDarkTheme ? '切换到浅色主题' : '切换到深色主题'" placement="bottom">
          <el-button type="text" @click="toggleTheme">
            <el-icon :size="18">
              <Sunny v-if="isDarkTheme" />
              <Moon v-else />
            </el-icon>
          </el-button>
        </el-tooltip>
      </div>

      <!-- 通知 -->
      <div v-if="settings.showNotification" class="header-item">
        <el-badge :value="notificationCount" :hidden="notificationCount === 0">
          <el-button type="text">
            <el-icon :size="18">
              <Bell />
            </el-icon>
          </el-button>
        </el-badge>
      </div>

      <!-- 用户菜单 -->
      <div class="header-item">
        <el-dropdown @command="handleUserCommand">
          <div class="user-info">
            <el-avatar
              :size="32"
              :src="user?.avatar"
              class="user-avatar"
            >
              <el-icon><User /></el-icon>
            </el-avatar>
            <span class="user-name">{{ user?.nickname || user?.username }}</span>
            <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="profile">
                <el-icon><User /></el-icon>
                个人资料
              </el-dropdown-item>
              <el-dropdown-item command="settings">
                <el-icon><Setting /></el-icon>
                设置
              </el-dropdown-item>
              <el-dropdown-item divided command="logout">
                <el-icon><SwitchButton /></el-icon>
                退出登录
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore, useAppStore } from '@/stores'
import {
  Fold,
  Expand,
  Search,
  FullScreen,
  Sunny,
  Moon,
  Bell,
  User,
  ArrowDown,
  Setting,
  SwitchButton
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'

const router = useRouter()
const authStore = useAuthStore()
const appStore = useAppStore()

// 响应式数据
const searchKeyword = ref('')
const notificationCount = ref(0)

// 计算属性
const user = computed(() => authStore.user)
const sidebarOpened = computed(() => appStore.sidebarOpened)
const isDarkTheme = computed(() => appStore.isDarkTheme)
const breadcrumbs = computed(() => appStore.breadcrumbs)
const settings = computed(() => appStore.settings)

// 方法
const toggleSidebar = () => {
  appStore.toggleSidebar()
}

const toggleTheme = () => {
  appStore.toggleTheme()
}

const toggleFullscreen = () => {
  appStore.toggleFullscreen()
}

const handleSearch = () => {
  if (searchKeyword.value.trim()) {
    router.push({
      name: 'DiaryList',
      query: { keyword: searchKeyword.value.trim() }
    })
  }
}

const handleUserCommand = async (command: string) => {
  switch (command) {
    case 'profile':
      router.push('/user/profile')
      break
    case 'settings':
      // 打开设置弹窗或跳转设置页面
      ElMessage.info('设置功能开发中...')
      break
    case 'logout':
      try {
        await ElMessageBox.confirm(
          '确定要退出登录吗？',
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
        await authStore.logout()
      } catch (error) {
        // 用户取消操作
      }
      break
  }
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/variables.scss';
.app-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 60px;
  padding: 0 var(--spacing-lg);
  background: var(--surface-color);
  border-bottom: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);

  &__left {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
  }

  &__right {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
  }
}

.sidebar-toggle {
  padding: var(--spacing-xs);
  color: var(--text-secondary);

  &:hover {
    color: var(--primary-color);
  }
}

.breadcrumb {
  font-size: var(--font-size-sm);
}

.header-item {
  display: flex;
  align-items: center;
}

.search-input {
  width: 200px;

  @media (max-width: 768px) {
    width: 150px;
  }
}

.user-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-md);
  cursor: pointer;
  transition: var(--transition-fast);

  &:hover {
    background: var(--primary-light);
  }
}

.user-avatar {
  flex-shrink: 0;
}

.user-name {
  font-size: var(--font-size-sm);
  color: var(--text-primary);
  max-width: 100px;
  @include text-ellipsis(1);

  @media (max-width: 768px) {
    display: none;
  }
}

.dropdown-icon {
  font-size: 12px;
  color: var(--text-secondary);
  transition: var(--transition-fast);
}

// 响应式设计
@media (max-width: 768px) {
  .app-header {
    padding: 0 var(--spacing-md);

    &__right {
      gap: var(--spacing-xs);
    }
  }

  .breadcrumb {
    display: none;
  }

  .search-input {
    width: 120px;
  }
}
</style>
