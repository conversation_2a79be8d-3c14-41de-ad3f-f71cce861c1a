/**
 * 应用常量定义
 */

// 应用信息
export const APP_INFO = {
  NAME: '她的日记',
  VERSION: '1.0.0',
  DESCRIPTION: '记录生活的美好时光',
  AUTHOR: 'Her Diary Team',
  COPYRIGHT: '© 2024 她的日记. All rights reserved.'
}

// API相关常量
export const API_CONFIG = {
  TIMEOUT: 10000,
  RETRY_TIMES: 3,
  RETRY_DELAY: 1000
}

// 响应状态码
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503,
  GATEWAY_TIMEOUT: 504
}

// 业务状态码
export const BUSINESS_CODE = {
  SUCCESS: 200,
  FAIL: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  SERVER_ERROR: 500
}

// 存储键名
export const STORAGE_KEYS = {
  TOKEN: 'auth_token',
  USER: 'auth_user',
  THEME: 'app_theme',
  LANGUAGE: 'app_language',
  LAYOUT: 'app_layout',
  SIDEBAR_STATUS: 'app_sidebar_status',
  SETTINGS: 'app_settings'
}

// 路由路径
export const ROUTES = {
  HOME: '/',
  LOGIN: '/auth/login',
  REGISTER: '/auth/register',
  DASHBOARD: '/dashboard',
  DIARY_LIST: '/diary/list',
  DIARY_CREATE: '/diary/create',
  DIARY_EDIT: '/diary/edit',
  DIARY_DETAIL: '/diary/detail',
  USER_PROFILE: '/user/profile',
  NOT_FOUND: '/404'
}

// 文件上传配置
export const UPLOAD_CONFIG = {
  MAX_SIZE: 10 * 1024 * 1024, // 10MB
  ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'],
  ALLOWED_FILE_TYPES: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
  IMAGE_MAX_SIZE: 5 * 1024 * 1024, // 5MB
  FILE_MAX_SIZE: 10 * 1024 * 1024 // 10MB
}

// 分页配置
export const PAGINATION = {
  DEFAULT_PAGE: 1,
  DEFAULT_PAGE_SIZE: 10,
  PAGE_SIZES: [10, 20, 50, 100],
  MAX_PAGE_SIZE: 100
}

// 日记相关常量
export const DIARY = {
  MOODS: [
    { value: 'happy', label: '开心', color: '#FFD700', icon: '😊' },
    { value: 'sad', label: '难过', color: '#87CEEB', icon: '😢' },
    { value: 'angry', label: '生气', color: '#FF6347', icon: '😠' },
    { value: 'excited', label: '兴奋', color: '#FF69B4', icon: '🤩' },
    { value: 'calm', label: '平静', color: '#98FB98', icon: '😌' },
    { value: 'anxious', label: '焦虑', color: '#DDA0DD', icon: '😰' },
    { value: 'grateful', label: '感恩', color: '#F0E68C', icon: '🙏' },
    { value: 'confused', label: '困惑', color: '#D3D3D3', icon: '😕' }
  ],
  
  WEATHER: [
    { value: 'sunny', label: '晴天', icon: '☀️' },
    { value: 'cloudy', label: '多云', icon: '☁️' },
    { value: 'rainy', label: '雨天', icon: '🌧️' },
    { value: 'snowy', label: '雪天', icon: '❄️' },
    { value: 'windy', label: '大风', icon: '💨' },
    { value: 'foggy', label: '雾天', icon: '🌫️' },
    { value: 'stormy', label: '暴风雨', icon: '⛈️' }
  ],

  TAG_COLORS: [
    '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
    '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9',
    '#F8C471', '#82E0AA', '#F1948A', '#85C1E9', '#D7BDE2'
  ],

  MAX_TITLE_LENGTH: 100,
  MAX_CONTENT_LENGTH: 10000,
  MAX_TAGS: 10
}

// 用户相关常量
export const USER = {
  USERNAME_MIN_LENGTH: 3,
  USERNAME_MAX_LENGTH: 20,
  PASSWORD_MIN_LENGTH: 6,
  PASSWORD_MAX_LENGTH: 20,
  NICKNAME_MAX_LENGTH: 20,
  AVATAR_MAX_SIZE: 2 * 1024 * 1024, // 2MB
  DEFAULT_AVATAR: '/default-avatar.png'
}

// 主题配置
export const THEME = {
  LIGHT: 'light',
  DARK: 'dark',
  AUTO: 'auto'
}

// 语言配置
export const LANGUAGE = {
  ZH_CN: 'zh-CN',
  EN_US: 'en-US'
}

// 布局配置
export const LAYOUT = {
  DEFAULT: 'default',
  COMPACT: 'compact',
  WIDE: 'wide'
}

// 设备类型
export const DEVICE = {
  MOBILE: 'mobile',
  TABLET: 'tablet',
  DESKTOP: 'desktop'
}

// 响应式断点
export const BREAKPOINTS = {
  XS: 0,
  SM: 576,
  MD: 768,
  LG: 992,
  XL: 1200,
  XXL: 1400
}

// 动画持续时间
export const ANIMATION = {
  FAST: 150,
  NORMAL: 300,
  SLOW: 500
}

// 正则表达式
export const REGEX = {
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PHONE: /^1[3-9]\d{9}$/,
  USERNAME: /^[a-zA-Z_][a-zA-Z0-9_]{2,19}$/,
  PASSWORD: /^(?=.*[a-zA-Z])(?=.*\d).{6,}$/,
  URL: /^https?:\/\/.+/,
  HEX_COLOR: /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/,
  CHINESE_NAME: /^[\u4e00-\u9fa5]{2,10}$/,
  ID_CARD: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,
  IP: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/
}

// 错误消息
export const ERROR_MESSAGES = {
  NETWORK_ERROR: '网络连接失败，请检查网络',
  SERVER_ERROR: '服务器错误，请稍后重试',
  UNAUTHORIZED: '登录已过期，请重新登录',
  FORBIDDEN: '没有权限访问该资源',
  NOT_FOUND: '请求的资源不存在',
  VALIDATION_ERROR: '数据验证失败',
  UPLOAD_ERROR: '文件上传失败',
  UNKNOWN_ERROR: '未知错误'
}

// 成功消息
export const SUCCESS_MESSAGES = {
  LOGIN_SUCCESS: '登录成功',
  LOGOUT_SUCCESS: '退出登录成功',
  REGISTER_SUCCESS: '注册成功',
  SAVE_SUCCESS: '保存成功',
  DELETE_SUCCESS: '删除成功',
  UPDATE_SUCCESS: '更新成功',
  UPLOAD_SUCCESS: '上传成功'
}

// 确认消息
export const CONFIRM_MESSAGES = {
  DELETE: '确定要删除吗？',
  LOGOUT: '确定要退出登录吗？',
  CANCEL: '确定要取消吗？',
  RESET: '确定要重置吗？'
}

// 默认配置
export const DEFAULT_CONFIG = {
  PAGE_SIZE: 10,
  TIMEOUT: 10000,
  DEBOUNCE_DELAY: 300,
  THROTTLE_DELAY: 1000,
  AUTO_SAVE_DELAY: 5000
}

// 缓存配置
export const CACHE = {
  USER_INFO_TTL: 24 * 60 * 60 * 1000, // 24小时
  API_CACHE_TTL: 5 * 60 * 1000, // 5分钟
  STATIC_CACHE_TTL: 7 * 24 * 60 * 60 * 1000 // 7天
}

// 导出所有常量
export default {
  APP_INFO,
  API_CONFIG,
  HTTP_STATUS,
  BUSINESS_CODE,
  STORAGE_KEYS,
  ROUTES,
  UPLOAD_CONFIG,
  PAGINATION,
  DIARY,
  USER,
  THEME,
  LANGUAGE,
  LAYOUT,
  DEVICE,
  BREAKPOINTS,
  ANIMATION,
  REGEX,
  ERROR_MESSAGES,
  SUCCESS_MESSAGES,
  CONFIRM_MESSAGES,
  DEFAULT_CONFIG,
  CACHE
}
