<template>
  <div class="loading-spinner" :class="{ 'is-fullscreen': fullscreen }">
    <div class="spinner-container">
      <!-- 自定义加载动画 -->
      <div class="spinner" :style="{ width: size + 'px', height: size + 'px' }">
        <div class="spinner-ring"></div>
        <div class="spinner-ring"></div>
        <div class="spinner-ring"></div>
      </div>
      
      <!-- 加载文本 -->
      <p v-if="text" class="loading-text">{{ text }}</p>
    </div>
    
    <!-- 背景遮罩 -->
    <div v-if="overlay" class="loading-overlay"></div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  size?: number
  text?: string
  fullscreen?: boolean
  overlay?: boolean
}

withDefaults(defineProps<Props>(), {
  size: 40,
  text: '加载中...',
  fullscreen: false,
  overlay: true
})
</script>

<style lang="scss" scoped>
@import '@/assets/styles/variables.scss';

.loading-spinner {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100px;

  &.is-fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: var(--z-modal);
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(2px);
  }
}

.spinner-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-md);
  z-index: 1;
}

.spinner {
  position: relative;
  display: inline-block;
}

.spinner-ring {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 3px solid transparent;
  border-top-color: var(--primary-color);
  border-radius: 50%;
  animation: spin 1.2s linear infinite;

  &:nth-child(1) {
    animation-delay: 0s;
    border-top-color: var(--primary-color);
  }

  &:nth-child(2) {
    animation-delay: 0.4s;
    border-top-color: var(--accent-color);
    transform: scale(0.8);
  }

  &:nth-child(3) {
    animation-delay: 0.8s;
    border-top-color: var(--primary-light);
    transform: scale(0.6);
  }
}

.loading-text {
  margin: 0;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  text-align: center;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(1px);
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 暗色主题适配
[data-theme="dark"] {
  .loading-spinner.is-fullscreen {
    background: rgba(0, 0, 0, 0.9);
  }

  .loading-overlay {
    background: rgba(0, 0, 0, 0.8);
  }
}

// 不同尺寸的加载器
.loading-spinner {
  &.size-small {
    .spinner {
      width: 24px;
      height: 24px;
    }
    
    .spinner-ring {
      border-width: 2px;
    }
  }

  &.size-large {
    .spinner {
      width: 60px;
      height: 60px;
    }
    
    .spinner-ring {
      border-width: 4px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .loading-spinner.is-fullscreen {
    .spinner {
      width: 32px;
      height: 32px;
    }
    
    .loading-text {
      font-size: var(--font-size-xs);
    }
  }
}
</style>
