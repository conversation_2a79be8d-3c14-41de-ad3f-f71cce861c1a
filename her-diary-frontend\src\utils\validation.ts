/**
 * 验证工具函数
 */

/**
 * 验证邮箱格式
 * @param email 邮箱地址
 * @returns 是否有效
 */
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

/**
 * 验证手机号格式（中国大陆）
 * @param phone 手机号
 * @returns 是否有效
 */
export const isValidPhone = (phone: string): boolean => {
  const phoneRegex = /^1[3-9]\d{9}$/
  return phoneRegex.test(phone)
}

/**
 * 验证用户名格式
 * @param username 用户名
 * @returns 是否有效
 */
export const isValidUsername = (username: string): boolean => {
  // 3-20位，只能包含字母、数字、下划线，不能以数字开头
  const usernameRegex = /^[a-zA-Z_][a-zA-Z0-9_]{2,19}$/
  return usernameRegex.test(username)
}

/**
 * 验证密码强度
 * @param password 密码
 * @returns 强度等级 0-4
 */
export const getPasswordStrength = (password: string): number => {
  let strength = 0
  
  if (password.length >= 6) strength++
  if (password.length >= 8) strength++
  if (/[a-z]/.test(password)) strength++
  if (/[A-Z]/.test(password)) strength++
  if (/\d/.test(password)) strength++
  if (/[^a-zA-Z0-9]/.test(password)) strength++
  
  return Math.min(strength, 4)
}

/**
 * 验证密码格式
 * @param password 密码
 * @returns 是否有效
 */
export const isValidPassword = (password: string): boolean => {
  // 至少6位，包含字母和数字
  const passwordRegex = /^(?=.*[a-zA-Z])(?=.*\d).{6,}$/
  return passwordRegex.test(password)
}

/**
 * 验证URL格式
 * @param url URL地址
 * @returns 是否有效
 */
export const isValidUrl = (url: string): boolean => {
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

/**
 * 验证身份证号格式（中国大陆）
 * @param idCard 身份证号
 * @returns 是否有效
 */
export const isValidIdCard = (idCard: string): boolean => {
  const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
  return idCardRegex.test(idCard)
}

/**
 * 验证IP地址格式
 * @param ip IP地址
 * @returns 是否有效
 */
export const isValidIP = (ip: string): boolean => {
  const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/
  return ipRegex.test(ip)
}

/**
 * 验证中文姓名格式
 * @param name 姓名
 * @returns 是否有效
 */
export const isValidChineseName = (name: string): boolean => {
  const nameRegex = /^[\u4e00-\u9fa5]{2,10}$/
  return nameRegex.test(name)
}

/**
 * 验证数字格式
 * @param value 值
 * @returns 是否为数字
 */
export const isNumber = (value: any): boolean => {
  return !isNaN(Number(value)) && isFinite(Number(value))
}

/**
 * 验证整数格式
 * @param value 值
 * @returns 是否为整数
 */
export const isInteger = (value: any): boolean => {
  return Number.isInteger(Number(value))
}

/**
 * 验证正整数格式
 * @param value 值
 * @returns 是否为正整数
 */
export const isPositiveInteger = (value: any): boolean => {
  const num = Number(value)
  return Number.isInteger(num) && num > 0
}

/**
 * 验证字符串长度
 * @param str 字符串
 * @param min 最小长度
 * @param max 最大长度
 * @returns 是否在范围内
 */
export const isValidLength = (str: string, min: number, max: number): boolean => {
  const length = str.length
  return length >= min && length <= max
}

/**
 * 验证是否为空
 * @param value 值
 * @returns 是否为空
 */
export const isEmpty = (value: any): boolean => {
  if (value === null || value === undefined) return true
  if (typeof value === 'string') return value.trim() === ''
  if (Array.isArray(value)) return value.length === 0
  if (typeof value === 'object') return Object.keys(value).length === 0
  return false
}

/**
 * 验证文件类型
 * @param file 文件
 * @param allowedTypes 允许的类型
 * @returns 是否允许
 */
export const isValidFileType = (file: File, allowedTypes: string[]): boolean => {
  return allowedTypes.includes(file.type)
}

/**
 * 验证文件大小
 * @param file 文件
 * @param maxSize 最大大小（字节）
 * @returns 是否在限制内
 */
export const isValidFileSize = (file: File, maxSize: number): boolean => {
  return file.size <= maxSize
}

/**
 * 验证图片文件
 * @param file 文件
 * @returns 是否为图片
 */
export const isImageFile = (file: File): boolean => {
  const imageTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
  return isValidFileType(file, imageTypes)
}

/**
 * 验证日期格式
 * @param dateString 日期字符串
 * @param format 格式
 * @returns 是否有效
 */
export const isValidDate = (dateString: string, format?: string): boolean => {
  if (format) {
    const regex = format
      .replace(/YYYY/g, '\\d{4}')
      .replace(/MM/g, '\\d{2}')
      .replace(/DD/g, '\\d{2}')
      .replace(/HH/g, '\\d{2}')
      .replace(/mm/g, '\\d{2}')
      .replace(/ss/g, '\\d{2}')
    return new RegExp(`^${regex}$`).test(dateString)
  }
  return !isNaN(Date.parse(dateString))
}

/**
 * 验证颜色值（十六进制）
 * @param color 颜色值
 * @returns 是否有效
 */
export const isValidHexColor = (color: string): boolean => {
  const hexColorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/
  return hexColorRegex.test(color)
}

/**
 * 验证JSON格式
 * @param jsonString JSON字符串
 * @returns 是否有效
 */
export const isValidJSON = (jsonString: string): boolean => {
  try {
    JSON.parse(jsonString)
    return true
  } catch {
    return false
  }
}

/**
 * 表单验证规则生成器
 */
export const createValidationRules = {
  required: (message: string = '此字段为必填项') => ({
    required: true,
    message,
    trigger: 'blur'
  }),

  email: (message: string = '请输入正确的邮箱地址') => ({
    validator: (rule: any, value: string, callback: any) => {
      if (value && !isValidEmail(value)) {
        callback(new Error(message))
      } else {
        callback()
      }
    },
    trigger: 'blur'
  }),

  phone: (message: string = '请输入正确的手机号') => ({
    validator: (rule: any, value: string, callback: any) => {
      if (value && !isValidPhone(value)) {
        callback(new Error(message))
      } else {
        callback()
      }
    },
    trigger: 'blur'
  }),

  username: (message: string = '用户名格式不正确') => ({
    validator: (rule: any, value: string, callback: any) => {
      if (value && !isValidUsername(value)) {
        callback(new Error(message))
      } else {
        callback()
      }
    },
    trigger: 'blur'
  }),

  password: (message: string = '密码必须包含字母和数字，至少6位') => ({
    validator: (rule: any, value: string, callback: any) => {
      if (value && !isValidPassword(value)) {
        callback(new Error(message))
      } else {
        callback()
      }
    },
    trigger: 'blur'
  }),

  length: (min: number, max: number, message?: string) => ({
    min,
    max,
    message: message || `长度在 ${min} 到 ${max} 个字符`,
    trigger: 'blur'
  }),

  pattern: (pattern: RegExp, message: string) => ({
    pattern,
    message,
    trigger: 'blur'
  })
}
