import { defineStore } from 'pinia'
import { ref, computed, readonly } from 'vue'
import { authApi, type User, type LoginParams, type RegisterParams } from '@/api'
import { ElMessage } from 'element-plus'
import router from '@/router'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref<string>('')
  const user = ref<User | null>(null)
  const loading = ref(false)

  // 计算属性
  const isAuthenticated = computed(() => !!token.value && !!user.value)
  const userInfo = computed(() => user.value)

  // 初始化：从localStorage恢复状态
  const initAuth = () => {
    const savedToken = localStorage.getItem('auth_token')
    const savedUser = localStorage.getItem('auth_user')
    
    if (savedToken) {
      token.value = savedToken
    }
    
    if (savedUser) {
      try {
        user.value = JSON.parse(savedUser)
      } catch (error) {
        console.error('解析用户信息失败:', error)
        clearAuth()
      }
    }
  }

  // 清除认证信息
  const clearAuth = () => {
    token.value = ''
    user.value = null
    localStorage.removeItem('auth_token')
    localStorage.removeItem('auth_user')
  }

  // 保存认证信息
  const saveAuth = (authToken: string, userInfo: User) => {
    token.value = authToken
    user.value = userInfo
    localStorage.setItem('auth_token', authToken)
    localStorage.setItem('auth_user', JSON.stringify(userInfo))
  }

  // 登录
  const login = async (params: LoginParams) => {
    try {
      loading.value = true
      const response = await authApi.login(params)
      
      if (response.code === 200) {
        const { token: authToken, user: userInfo } = response.data
        saveAuth(authToken, userInfo)
        
        ElMessage.success('登录成功')
        
        // 跳转到重定向页面或首页
        const redirect = router.currentRoute.value.query.redirect as string
        router.push(redirect || '/dashboard')
        
        return true
      }
      return false
    } catch (error) {
      console.error('登录失败:', error)
      return false
    } finally {
      loading.value = false
    }
  }

  // 注册
  const register = async (params: RegisterParams) => {
    try {
      loading.value = true
      const response = await authApi.register(params)
      
      if (response.code === 200) {
        ElMessage.success('注册成功，请登录')
        router.push('/auth/login')
        return true
      }
      return false
    } catch (error) {
      console.error('注册失败:', error)
      return false
    } finally {
      loading.value = false
    }
  }

  // 登出
  const logout = async () => {
    try {
      // 调用后端登出接口
      await authApi.logout()
    } catch (error) {
      console.error('登出接口调用失败:', error)
    } finally {
      // 无论接口是否成功，都清除本地状态
      clearAuth()
      ElMessage.success('已退出登录')
      router.push('/auth/login')
    }
  }

  // 获取用户信息
  const fetchUserInfo = async () => {
    try {
      const response = await authApi.getUserInfo()
      
      if (response.code === 200) {
        user.value = response.data
        localStorage.setItem('auth_user', JSON.stringify(response.data))
        return response.data
      } else {
        throw new Error('获取用户信息失败')
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
      clearAuth()
      throw error
    }
  }

  // 更新用户信息
  const updateUserInfo = (userInfo: Partial<User>) => {
    if (user.value) {
      user.value = { ...user.value, ...userInfo }
      localStorage.setItem('auth_user', JSON.stringify(user.value))
    }
  }

  // 刷新token
  const refreshToken = async () => {
    try {
      const response = await authApi.refreshToken()
      
      if (response.code === 200) {
        token.value = response.data.token
        localStorage.setItem('auth_token', response.data.token)
        return true
      }
      return false
    } catch (error) {
      console.error('刷新token失败:', error)
      clearAuth()
      return false
    }
  }

  // 修改密码
  const changePassword = async (params: {
    oldPassword: string
    newPassword: string
    confirmPassword: string
  }) => {
    try {
      loading.value = true
      const response = await authApi.changePassword(params)
      
      if (response.code === 200) {
        ElMessage.success('密码修改成功，请重新登录')
        logout()
        return true
      }
      return false
    } catch (error) {
      console.error('修改密码失败:', error)
      return false
    } finally {
      loading.value = false
    }
  }

  // 忘记密码
  const forgotPassword = async (email: string) => {
    try {
      loading.value = true
      const response = await authApi.forgotPassword(email)
      
      if (response.code === 200) {
        ElMessage.success('重置密码邮件已发送，请查收')
        return true
      }
      return false
    } catch (error) {
      console.error('发送重置密码邮件失败:', error)
      return false
    } finally {
      loading.value = false
    }
  }

  // 重置密码
  const resetPassword = async (params: {
    token: string
    password: string
    confirmPassword: string
  }) => {
    try {
      loading.value = true
      const response = await authApi.resetPassword(params)
      
      if (response.code === 200) {
        ElMessage.success('密码重置成功，请登录')
        router.push('/auth/login')
        return true
      }
      return false
    } catch (error) {
      console.error('重置密码失败:', error)
      return false
    } finally {
      loading.value = false
    }
  }

  // 初始化认证状态
  initAuth()

  return {
    // 状态
    token: readonly(token),
    user: readonly(user),
    loading: readonly(loading),
    
    // 计算属性
    isAuthenticated,
    userInfo,
    
    // 方法
    login,
    register,
    logout,
    fetchUserInfo,
    updateUserInfo,
    refreshToken,
    changePassword,
    forgotPassword,
    resetPassword,
    clearAuth
  }
})
