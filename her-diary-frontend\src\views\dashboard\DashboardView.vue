<template>
  <div class="dashboard">
    <div class="dashboard-header">
      <h1 class="page-title">仪表板</h1>
      <p class="page-subtitle">欢迎回来，{{ user?.nickname || user?.username }}！</p>
    </div>

    <div class="dashboard-content">
      <!-- 统计卡片 -->
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-icon">
            <el-icon><Document /></el-icon>
          </div>
          <div class="stat-content">
            <h3 class="stat-number">{{ stats.totalDiaries || 0 }}</h3>
            <p class="stat-label">总日记数</p>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon">
            <el-icon><EditPen /></el-icon>
          </div>
          <div class="stat-content">
            <h3 class="stat-number">{{ stats.totalWords || 0 }}</h3>
            <p class="stat-label">总字数</p>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon">
            <el-icon><CollectionTag /></el-icon>
          </div>
          <div class="stat-content">
            <h3 class="stat-number">{{ stats.totalTags || 0 }}</h3>
            <p class="stat-label">标签数</p>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon">
            <el-icon><Calendar /></el-icon>
          </div>
          <div class="stat-content">
            <h3 class="stat-number">{{ stats.writingDays || 0 }}</h3>
            <p class="stat-label">写作天数</p>
          </div>
        </div>
      </div>

      <!-- 快速操作 -->
      <div class="quick-actions">
        <h2 class="section-title">快速操作</h2>
        <div class="action-buttons">
          <el-button type="primary" size="large" @click="goToWrite">
            <el-icon><EditPen /></el-icon>
            写日记
          </el-button>
          <el-button size="large" @click="goToDiaryList">
            <el-icon><Document /></el-icon>
            查看日记
          </el-button>
          <el-button size="large" @click="viewRandomDiary">
            <el-icon><Refresh /></el-icon>
            随机回顾
          </el-button>
        </div>
      </div>

      <!-- 最近日记 -->
      <div class="recent-diaries">
        <h2 class="section-title">最近日记</h2>
        <div v-if="loading" class="loading-container">
          <el-skeleton :rows="3" animated />
        </div>
        <div v-else-if="recentDiaries.length === 0" class="empty-state">
          <el-empty description="还没有日记，开始写第一篇吧！">
            <el-button type="primary" @click="goToWrite">写日记</el-button>
          </el-empty>
        </div>
        <div v-else class="diary-list">
          <div
            v-for="diary in recentDiaries"
            :key="diary.id"
            class="diary-item"
            @click="goToDiaryDetail(diary.id)"
          >
            <div class="diary-header">
              <h3 class="diary-title">{{ diary.title }}</h3>
              <span class="diary-date">{{ formatDate(diary.created_at, 'MM-DD HH:mm') }}</span>
            </div>
            <p class="diary-preview">{{ truncateString(diary.content, 100) }}</p>
            <div v-if="diary.tags && diary.tags.length > 0" class="diary-tags">
              <el-tag
                v-for="tag in diary.tags.slice(0, 3)"
                :key="tag.id"
                size="small"
                :color="tag.color"
              >
                {{ tag.name }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore, useDiaryStore } from '@/stores'
import { formatDate, truncateString } from '@/utils'
import { ElMessage } from 'element-plus'
import {
  Document,
  EditPen,
  CollectionTag,
  Calendar,
  Refresh
} from '@element-plus/icons-vue'

const router = useRouter()
const authStore = useAuthStore()
const diaryStore = useDiaryStore()

// 响应式数据
const loading = ref(false)
const stats = ref({
  totalDiaries: 0,
  totalWords: 0,
  totalTags: 0,
  writingDays: 0
})

// 计算属性
const user = computed(() => authStore.user)
const recentDiaries = computed(() => diaryStore.diaries.slice(0, 5))

// 方法
const goToWrite = () => {
  router.push('/diary/create')
}

const goToDiaryList = () => {
  router.push('/diary/list')
}

const goToDiaryDetail = (id: number) => {
  router.push(`/diary/detail/${id}`)
}

const viewRandomDiary = async () => {
  try {
    const diary = await diaryStore.fetchRandomDiary()
    if (diary) {
      router.push(`/diary/detail/${diary.id}`)
    } else {
      ElMessage.info('还没有日记可以回顾')
    }
  } catch (error) {
    ElMessage.error('获取随机日记失败')
  }
}

const loadDashboardData = async () => {
  try {
    loading.value = true
    
    // 加载最近日记
    await diaryStore.fetchDiaries({ pageSize: 5 })
    
    // 这里可以加载统计数据
    // const statsData = await dashboardApi.getStats()
    // stats.value = statsData.data
    
  } catch (error) {
    console.error('加载仪表板数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  loadDashboardData()
})
</script>

<style lang="scss" scoped>
@import '@/assets/styles/variables.scss';

.dashboard {
  max-width: 1200px;
  margin: 0 auto;
}

.dashboard-header {
  margin-bottom: var(--spacing-2xl);

  .page-title {
    font-size: var(--font-size-3xl);
    font-weight: bold;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-sm) 0;
  }

  .page-subtitle {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    margin: 0;
  }
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-2xl);
}

.stat-card {
  background: var(--surface-color);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  transition: var(--transition-normal);

  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
  }

  .stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
  }

  .stat-content {
    .stat-number {
      font-size: var(--font-size-2xl);
      font-weight: bold;
      color: var(--text-primary);
      margin: 0 0 var(--spacing-xs) 0;
    }

    .stat-label {
      font-size: var(--font-size-sm);
      color: var(--text-secondary);
      margin: 0;
    }
  }
}

.section-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-lg) 0;
}

.quick-actions {
  margin-bottom: var(--spacing-2xl);

  .action-buttons {
    display: flex;
    gap: var(--spacing-md);
    flex-wrap: wrap;
  }
}

.recent-diaries {
  .loading-container {
    background: var(--surface-color);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
  }

  .empty-state {
    background: var(--surface-color);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-2xl);
    text-align: center;
  }

  .diary-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .diary-item {
    background: var(--surface-color);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
    cursor: pointer;
    transition: var(--transition-normal);

    &:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-md);
    }

    .diary-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: var(--spacing-sm);

      .diary-title {
        font-size: var(--font-size-lg);
        font-weight: 600;
        color: var(--text-primary);
        margin: 0;
        @include text-ellipsis(1);
      }

      .diary-date {
        font-size: var(--font-size-sm);
        color: var(--text-secondary);
        white-space: nowrap;
      }
    }

    .diary-preview {
      font-size: var(--font-size-base);
      color: var(--text-secondary);
      line-height: var(--line-height-loose);
      margin: 0 0 var(--spacing-sm) 0;
    }

    .diary-tags {
      display: flex;
      gap: var(--spacing-xs);
      flex-wrap: wrap;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
  }

  .stat-card {
    padding: var(--spacing-md);

    .stat-icon {
      width: 50px;
      height: 50px;
      font-size: 20px;
    }
  }

  .action-buttons {
    flex-direction: column;

    .el-button {
      width: 100%;
    }
  }

  .diary-item {
    .diary-header {
      flex-direction: column;
      align-items: flex-start;
      gap: var(--spacing-xs);
    }
  }
}
</style>
