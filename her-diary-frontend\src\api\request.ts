import axios, { type AxiosInstance, type AxiosRequestConfig, type AxiosResponse, type AxiosError } from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useAuthStore } from '@/stores/auth'

// 响应数据接口
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

// 请求配置接口
export interface RequestConfig extends AxiosRequestConfig {
  showLoading?: boolean
  showError?: boolean
  showSuccess?: boolean
}

// 创建axios实例
const service: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_APP_BASE_API,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json;charset=UTF-8'
  }
})

// 请求拦截器
service.interceptors.request.use(
  (config: any) => {
    // 添加认证token
    const authStore = useAuthStore()
    if (authStore.token) {
      config.headers.Authorization = `Bearer ${authStore.token}`
    }

    // 添加时间戳防止缓存
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now()
      }
    }

    // 显示加载提示
    if (config.showLoading !== false) {
      // 可以在这里添加全局loading
    }

    return config
  },
  (error: AxiosError) => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    const { data, config } = response
    const requestConfig = config as RequestConfig

    // 隐藏加载提示
    if (requestConfig.showLoading !== false) {
      // 可以在这里隐藏全局loading
    }

    // 检查业务状态码
    if (data.code === 200) {
      // 显示成功消息
      if (requestConfig.showSuccess && data.message) {
        ElMessage.success(data.message)
      }
      return response
    } else if (data.code === 401) {
      // 未授权，清除token并跳转到登录页
      const authStore = useAuthStore()
      authStore.logout()
      ElMessage.error('登录已过期，请重新登录')
      return Promise.reject(new Error('未授权'))
    } else if (data.code === 403) {
      ElMessage.error('没有权限访问该资源')
      return Promise.reject(new Error('权限不足'))
    } else {
      // 其他业务错误
      if (requestConfig.showError !== false) {
        ElMessage.error(data.message || '请求失败')
      }
      return Promise.reject(new Error(data.message || '请求失败'))
    }
  },
  (error: AxiosError) => {
    const { config } = error
    const requestConfig = config as RequestConfig

    // 隐藏加载提示
    if (requestConfig?.showLoading !== false) {
      // 可以在这里隐藏全局loading
    }

    let message = '网络错误'

    if (error.response) {
      const { status, data } = error.response
      switch (status) {
        case 400:
          message = '请求参数错误'
          break
        case 401:
          message = '未授权，请重新登录'
          const authStore = useAuthStore()
          authStore.logout()
          break
        case 403:
          message = '权限不足'
          break
        case 404:
          message = '请求的资源不存在'
          break
        case 500:
          message = '服务器内部错误'
          break
        case 502:
          message = '网关错误'
          break
        case 503:
          message = '服务不可用'
          break
        case 504:
          message = '网关超时'
          break
        default:
          message = (data as any)?.message || `请求失败 (${status})`
      }
    } else if (error.request) {
      message = '网络连接失败，请检查网络'
    } else {
      message = error.message || '请求配置错误'
    }

    // 显示错误消息
    if (requestConfig?.showError !== false) {
      ElMessage.error(message)
    }

    console.error('响应错误:', error)
    return Promise.reject(error)
  }
)

// 封装请求方法
class Request {
  // GET请求
  get<T = any>(url: string, params?: any, config?: RequestConfig): Promise<ApiResponse<T>> {
    return service.get(url, { params, ...config }).then(response => response.data)
  }

  // POST请求
  post<T = any>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>> {
    return service.post(url, data, config).then(response => response.data)
  }

  // PUT请求
  put<T = any>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>> {
    return service.put(url, data, config).then(response => response.data)
  }

  // DELETE请求
  delete<T = any>(url: string, params?: any, config?: RequestConfig): Promise<ApiResponse<T>> {
    return service.delete(url, { params, ...config }).then(response => response.data)
  }

  // PATCH请求
  patch<T = any>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>> {
    return service.patch(url, data, config).then(response => response.data)
  }

  // 上传文件
  upload<T = any>(url: string, file: File, config?: RequestConfig): Promise<ApiResponse<T>> {
    const formData = new FormData()
    formData.append('file', file)

    return service.post(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      ...config
    }).then(response => response.data)
  }

  // 下载文件
  download(url: string, params?: any, filename?: string): Promise<void> {
    return service.get(url, {
      params,
      responseType: 'blob'
    }).then((response: any) => {
      const blob = new Blob([response.data])
      const downloadUrl = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = downloadUrl
      link.download = filename || 'download'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(downloadUrl)
    })
  }
}

// 创建请求实例
const request = new Request()

export default request
export { service }
