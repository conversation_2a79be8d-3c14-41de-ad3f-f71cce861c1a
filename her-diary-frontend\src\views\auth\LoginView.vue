<template>
  <div class="login-view">
    <div class="login-container">
      <!-- 左侧装饰区域 -->
      <div class="login-decoration">
        <div class="decoration-content">
          <h1 class="decoration-title">她的日记</h1>
          <p class="decoration-subtitle">记录生活的美好时光</p>
          <div class="decoration-features">
            <div class="feature-item">
              <el-icon><EditPen /></el-icon>
              <span>随时记录心情</span>
            </div>
            <div class="feature-item">
              <el-icon><CollectionTag /></el-icon>
              <span>智能标签分类</span>
            </div>
            <div class="feature-item">
              <el-icon><TrendCharts /></el-icon>
              <span>数据统计分析</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧登录表单 -->
      <div class="login-form-container">
        <div class="login-form">
          <div class="form-header">
            <h2 class="form-title">欢迎回来</h2>
            <p class="form-subtitle">请登录您的账户</p>
          </div>

          <el-form
            ref="loginFormRef"
            :model="loginForm"
            :rules="loginRules"
            class="login-form-content"
            @submit.prevent="handleLogin"
          >
            <el-form-item prop="username">
              <el-input
                v-model="loginForm.username"
                placeholder="请输入用户名或邮箱"
                size="large"
                clearable
              >
                <template #prefix>
                  <el-icon><User /></el-icon>
                </template>
              </el-input>
            </el-form-item>

            <el-form-item prop="password">
              <el-input
                v-model="loginForm.password"
                type="password"
                placeholder="请输入密码"
                size="large"
                show-password
                clearable
                @keyup.enter="handleLogin"
              >
                <template #prefix>
                  <el-icon><Lock /></el-icon>
                </template>
              </el-input>
            </el-form-item>

            <div class="form-options">
              <el-checkbox v-model="rememberMe">记住我</el-checkbox>
              <el-link type="primary" @click="showForgotPassword">
                忘记密码？
              </el-link>
            </div>

            <el-button
              type="primary"
              size="large"
              class="login-button"
              :loading="loading"
              @click="handleLogin"
            >
              {{ loading ? '登录中...' : '登录' }}
            </el-button>
          </el-form>

          <div class="form-footer">
            <p class="register-tip">
              还没有账户？
              <router-link to="/auth/register" class="register-link">
                立即注册
              </router-link>
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- 忘记密码弹窗 -->
    <el-dialog
      v-model="forgotPasswordVisible"
      title="忘记密码"
      width="400px"
      :before-close="closeForgotPassword"
    >
      <el-form
        ref="forgotFormRef"
        :model="forgotForm"
        :rules="forgotRules"
      >
        <el-form-item prop="email">
          <el-input
            v-model="forgotForm.email"
            placeholder="请输入注册邮箱"
            size="large"
          >
            <template #prefix>
              <el-icon><Message /></el-icon>
            </template>
          </el-input>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="closeForgotPassword">取消</el-button>
        <el-button
          type="primary"
          :loading="forgotLoading"
          @click="handleForgotPassword"
        >
          发送重置邮件
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '@/stores'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import {
  User,
  Lock,
  Message,
  EditPen,
  CollectionTag,
  TrendCharts
} from '@element-plus/icons-vue'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

// 表单引用
const loginFormRef = ref<FormInstance>()
const forgotFormRef = ref<FormInstance>()

// 响应式数据
const loginForm = reactive({
  username: '',
  password: ''
})

const forgotForm = reactive({
  email: ''
})

const rememberMe = ref(false)
const forgotPasswordVisible = ref(false)
const forgotLoading = ref(false)

// 计算属性
const loading = computed(() => authStore.loading)

// 表单验证规则
const loginRules: FormRules = {
  username: [
    { required: true, message: '请输入用户名或邮箱', trigger: 'blur' },
    { min: 3, max: 50, message: '用户名长度在 3 到 50 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ]
}

const forgotRules: FormRules = {
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ]
}

// 方法
const handleLogin = async () => {
  if (!loginFormRef.value) return

  try {
    const valid = await loginFormRef.value.validate()
    if (!valid) return

    const success = await authStore.login(loginForm)
    if (success) {
      ElMessage.success('登录成功')
      // 跳转到重定向页面或首页
      const redirect = route.query.redirect as string
      router.push(redirect || '/dashboard')
    }
  } catch (error) {
    console.error('登录失败:', error)
  }
}

const showForgotPassword = () => {
  forgotPasswordVisible.value = true
}

const closeForgotPassword = () => {
  forgotPasswordVisible.value = false
  forgotForm.email = ''
  forgotFormRef.value?.clearValidate()
}

const handleForgotPassword = async () => {
  if (!forgotFormRef.value) return

  try {
    const valid = await forgotFormRef.value.validate()
    if (!valid) return

    forgotLoading.value = true
    const success = await authStore.forgotPassword(forgotForm.email)
    
    if (success) {
      ElMessage.success('重置密码邮件已发送，请查收')
      closeForgotPassword()
    }
  } catch (error) {
    console.error('发送重置邮件失败:', error)
  } finally {
    forgotLoading.value = false
  }
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/variables.scss';

.login-view {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--primary-light) 0%, var(--accent-light) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-lg);
}

.login-container {
  width: 100%;
  max-width: 1000px;
  background: var(--surface-color);
  border-radius: var(--border-radius-xl);
  box-shadow: var(--shadow-xl);
  overflow: hidden;
  display: flex;
  min-height: 600px;
}

.login-decoration {
  flex: 1;
  background: var(--gradient-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-2xl);
  color: white;

  .decoration-content {
    text-align: center;
    max-width: 400px;
  }

  .decoration-title {
    font-size: var(--font-size-3xl);
    font-weight: bold;
    margin: 0 0 var(--spacing-md) 0;
  }

  .decoration-subtitle {
    font-size: var(--font-size-lg);
    margin: 0 0 var(--spacing-2xl) 0;
    opacity: 0.9;
  }

  .decoration-features {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
  }

  .feature-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    font-size: var(--font-size-base);

    .el-icon {
      font-size: 20px;
    }
  }
}

.login-form-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-2xl);
}

.login-form {
  width: 100%;
  max-width: 400px;

  .form-header {
    text-align: center;
    margin-bottom: var(--spacing-2xl);

    .form-title {
      font-size: var(--font-size-2xl);
      font-weight: bold;
      color: var(--text-primary);
      margin: 0 0 var(--spacing-sm) 0;
    }

    .form-subtitle {
      font-size: var(--font-size-base);
      color: var(--text-secondary);
      margin: 0;
    }
  }

  .login-form-content {
    .el-form-item {
      margin-bottom: var(--spacing-lg);
    }
  }

  .form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xl);
  }

  .login-button {
    width: 100%;
    height: 48px;
    font-size: var(--font-size-base);
    font-weight: 500;
  }

  .form-footer {
    text-align: center;
    margin-top: var(--spacing-xl);

    .register-tip {
      font-size: var(--font-size-sm);
      color: var(--text-secondary);
      margin: 0;

      .register-link {
        color: var(--primary-color);
        text-decoration: none;
        font-weight: 500;

        &:hover {
          text-decoration: underline;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .login-view {
    padding: var(--spacing-md);
  }

  .login-container {
    flex-direction: column;
    min-height: auto;
  }

  .login-decoration {
    padding: var(--spacing-xl) var(--spacing-lg);

    .decoration-title {
      font-size: var(--font-size-2xl);
    }

    .decoration-features {
      flex-direction: row;
      flex-wrap: wrap;
      justify-content: center;
      gap: var(--spacing-md);

      .feature-item {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-xs);
        font-size: var(--font-size-sm);
      }
    }
  }

  .login-form-container {
    padding: var(--spacing-xl) var(--spacing-lg);
  }
}
</style>
