<template>
  <div class="diary-detail-view">
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="8" animated />
    </div>
    
    <div v-else-if="!diary" class="error-state">
      <el-result
        icon="warning"
        title="日记不存在"
        sub-title="您访问的日记可能已被删除或不存在"
      >
        <template #extra>
          <el-button type="primary" @click="goBack">返回</el-button>
        </template>
      </el-result>
    </div>
    
    <div v-else class="diary-content">
      <!-- 头部操作栏 -->
      <div class="page-header">
        <el-button @click="goBack">
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
        <div class="header-actions">
          <el-button @click="goToEdit">
            <el-icon><Edit /></el-icon>
            编辑
          </el-button>
          <el-button type="danger" @click="handleDelete">
            <el-icon><Delete /></el-icon>
            删除
          </el-button>
        </div>
      </div>

      <!-- 日记内容 -->
      <article class="diary-article">
        <!-- 标题和元信息 -->
        <header class="article-header">
          <h1 class="article-title">{{ diary.title }}</h1>
          <div class="article-meta">
            <div class="meta-item">
              <el-icon><Calendar /></el-icon>
              <span>{{ formatDate(diary.created_at, 'YYYY年MM月DD日 HH:mm') }}</span>
            </div>
            <div v-if="diary.mood" class="meta-item">
              <span class="mood-icon">{{ getMoodIcon(diary.mood) }}</span>
              <span>{{ getMoodLabel(diary.mood) }}</span>
            </div>
            <div v-if="diary.weather" class="meta-item">
              <span class="weather-icon">{{ getWeatherIcon(diary.weather) }}</span>
              <span>{{ getWeatherLabel(diary.weather) }}</span>
            </div>
            <div v-if="!diary.is_public" class="meta-item">
              <el-icon><Lock /></el-icon>
              <span>私密</span>
            </div>
          </div>
        </header>

        <!-- 标签 -->
        <div v-if="diary.tags && diary.tags.length > 0" class="article-tags">
          <el-tag
            v-for="tag in diary.tags"
            :key="tag.id"
            :color="tag.color"
            class="tag-item"
          >
            {{ tag.name }}
          </el-tag>
        </div>

        <!-- 正文内容 -->
        <div class="article-body">
          <div class="content-text">{{ diary.content }}</div>
        </div>

        <!-- 底部信息 -->
        <footer class="article-footer">
          <div class="footer-stats">
            <span class="stat-item">字数：{{ diary.content.length }}</span>
            <span v-if="diary.updated_at !== diary.created_at" class="stat-item">
              最后编辑：{{ formatRelativeTime(diary.updated_at) }}
            </span>
          </div>
        </footer>
      </article>

      <!-- 相关推荐 -->
      <div v-if="relatedDiaries.length > 0" class="related-section">
        <h3 class="section-title">相关日记</h3>
        <div class="related-list">
          <div
            v-for="relatedDiary in relatedDiaries"
            :key="relatedDiary.id"
            class="related-item"
            @click="goToDetail(relatedDiary.id)"
          >
            <h4 class="related-title">{{ relatedDiary.title }}</h4>
            <p class="related-date">{{ smartFormatDate(relatedDiary.created_at) }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useDiaryStore } from '@/stores'
import { formatDate, formatRelativeTime, smartFormatDate } from '@/utils'
import { DIARY } from '@/utils/constants'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowLeft,
  Edit,
  Delete,
  Calendar,
  Lock
} from '@element-plus/icons-vue'

const router = useRouter()
const route = useRoute()
const diaryStore = useDiaryStore()

// 响应式数据
const relatedDiaries = ref([])

// 计算属性
const diary = computed(() => diaryStore.currentDiary)
const loading = computed(() => diaryStore.loading)

// 方法
const goBack = () => {
  router.back()
}

const goToEdit = () => {
  router.push(`/diary/edit/${diary.value?.id}`)
}

const goToDetail = (id: number) => {
  router.push(`/diary/detail/${id}`)
}

const handleDelete = async () => {
  if (!diary.value) return

  try {
    await ElMessageBox.confirm(
      '确定要删除这篇日记吗？删除后无法恢复。',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const success = await diaryStore.deleteDiary(diary.value.id)
    if (success) {
      ElMessage.success('日记删除成功')
      router.push('/diary/list')
    }
  } catch (error) {
    // 用户取消操作
  }
}

const getMoodIcon = (mood: string) => {
  const moodItem = DIARY.MOODS.find(item => item.value === mood)
  return moodItem?.icon || ''
}

const getMoodLabel = (mood: string) => {
  const moodItem = DIARY.MOODS.find(item => item.value === mood)
  return moodItem?.label || ''
}

const getWeatherIcon = (weather: string) => {
  const weatherItem = DIARY.WEATHER.find(item => item.value === weather)
  return weatherItem?.icon || ''
}

const getWeatherLabel = (weather: string) => {
  const weatherItem = DIARY.WEATHER.find(item => item.value === weather)
  return weatherItem?.label || ''
}

const loadDiary = async () => {
  const id = Number(route.params.id)
  if (id) {
    await diaryStore.fetchDiaryDetail(id)
  }
}

// 生命周期
onMounted(() => {
  loadDiary()
})
</script>

<style lang="scss" scoped>
@import '@/assets/styles/variables.scss';

.diary-detail-view {
  max-width: 800px;
  margin: 0 auto;
}

.loading-container {
  background: var(--surface-color);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-2xl);
}

.error-state {
  background: var(--surface-color);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-2xl);
  text-align: center;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xl);

  .header-actions {
    display: flex;
    gap: var(--spacing-sm);
  }
}

.diary-article {
  background: var(--surface-color);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-2xl);
  box-shadow: var(--shadow-md);
  margin-bottom: var(--spacing-xl);

  .article-header {
    margin-bottom: var(--spacing-xl);

    .article-title {
      font-size: var(--font-size-3xl);
      font-weight: bold;
      color: var(--text-primary);
      margin: 0 0 var(--spacing-lg) 0;
      line-height: var(--line-height-tight);
    }

    .article-meta {
      display: flex;
      flex-wrap: wrap;
      gap: var(--spacing-md);

      .meta-item {
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        font-size: var(--font-size-sm);
        color: var(--text-secondary);

        .mood-icon,
        .weather-icon {
          font-size: 16px;
        }
      }
    }
  }

  .article-tags {
    margin-bottom: var(--spacing-xl);

    .tag-item {
      margin-right: var(--spacing-sm);
      margin-bottom: var(--spacing-xs);
    }
  }

  .article-body {
    margin-bottom: var(--spacing-xl);

    .content-text {
      font-size: var(--font-size-base);
      line-height: var(--line-height-loose);
      color: var(--text-primary);
      white-space: pre-wrap;
      word-break: break-word;
    }
  }

  .article-footer {
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-color);

    .footer-stats {
      display: flex;
      gap: var(--spacing-lg);
      font-size: var(--font-size-sm);
      color: var(--text-secondary);

      .stat-item {
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
      }
    }
  }
}

.related-section {
  background: var(--surface-color);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-md);

  .section-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-lg) 0;
  }

  .related-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .related-item {
    padding: var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: var(--transition-fast);

    &:hover {
      border-color: var(--primary-color);
      background: var(--primary-light);
    }

    .related-title {
      font-size: var(--font-size-base);
      font-weight: 500;
      color: var(--text-primary);
      margin: 0 0 var(--spacing-xs) 0;
      @include text-ellipsis(1);
    }

    .related-date {
      font-size: var(--font-size-sm);
      color: var(--text-secondary);
      margin: 0;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-md);

    .header-actions {
      width: 100%;
      justify-content: flex-end;
    }
  }

  .diary-article {
    padding: var(--spacing-lg);

    .article-header {
      .article-title {
        font-size: var(--font-size-2xl);
      }

      .article-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
      }
    }
  }

  .related-section {
    padding: var(--spacing-lg);
  }
}
</style>
